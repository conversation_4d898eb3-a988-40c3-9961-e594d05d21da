#include "stm32f10x.h"                  // Device header
#include "Motor.h"

void Track_Init(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA,ENABLE);
	
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_3 | GPIO_Pin_4;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA,&GPIO_InitStructure);
}

// PA4��Ӧ����ߵĴ�����
uint8_t Track_Get_Left2(void)
{
	return GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_4);
}

// PA3��Ӧ��ߵĴ�����
uint8_t Track_Get_Left1(void)
{
	return GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_3);
}

// PA2��Ӧ�м�Ĵ�����
uint8_t Track_Get_Center(void)
{
	return GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_2);
}

// PA1��Ӧ�ұߵĴ�����
uint8_t Track_Get_Right1(void)
{
	return GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_1);
}

// PA0��Ӧ���ұߵĴ�����
uint8_t Track_Get_Right2(void)
{
	return GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_0);
}

void Track(void)
{
	// ��ȡ�����������״̬ (0��ʾ��⵽���ߣ�1��ʾ��ɫ)
	uint8_t sensor_left2 = Track_Get_Left2();   // PA4 ����
	uint8_t sensor_left1 = Track_Get_Left1();   // PA3 ��
	uint8_t sensor_center = Track_Get_Center();  // PA2 ��
	uint8_t sensor_right1 = Track_Get_Right1(); // PA1 ��
	uint8_t sensor_right2 = Track_Get_Right2(); // PA0 ����

	// ��ϴ�����״̬�����ڸ���ȷ���ж�
	uint8_t sensor_pattern = (sensor_left2 << 4) | (sensor_left1 << 3) | (sensor_center << 2) | (sensor_right1 << 1) | sensor_right2;

	// ���ݴ��������ģʽ����ѭ��
	switch(sensor_pattern)
	{
		// ���������ֻ���м䴫������⵽���� (11011)
		case 0b11011:
			Motor_Run(80);  // ֱ��
			break;

		// �м�+��1��⵽ (10011) - ����΢ƫ�ң���Ҫ��΢��ת
		case 0b10011:
			Set_Motor(40,70); // �����������ֿ죬��΢��ת
			break;

		// ֻ����1��⵽ (10111) - ������ƫ�ң���Ҫ��ת
		case 0b10111:
			Set_Motor(20,80); // ���ֺ��������ֿ죬��ת
			break;

		// �м�+��1��⵽ (11001) - ����΢ƫ����Ҫ��΢��ת
		case 0b11001:
			Set_Motor(70,40); // ���ֿ죬����������΢��ת
			break;

		// ֻ����1��⵽ (11101) - ������ƫ����Ҫ��ת
		case 0b11101:
			Set_Motor(80,20); // ���ֿ죬���ֺ�������ת
			break;

		// ֻ������߼�⵽ (01111) - ������ƫ�ң���Ҫ�����ת
		case 0b01111:
			Set_Motor(0,50); // ֻ������ת�������ת
			break;

		// ֻ�����ұ߼�⵽ (11110) - ������ƫ����Ҫ�����ת
		case 0b11110:
			Set_Motor(50,0);  // ֻ������ת�������ת
			break;

		// ��2+��1��⵽ (00111) - ��ת�������ƫ�ƣ���Ҫԭ����ת
		case 0b00111:
			Set_Motor(0,50);  // ԭ����ת
			break;

		// ��1+��2��⵽ (11100) - ��ת�������ƫ�ƣ���Ҫԭ����ת
		case 0b11100:
			Set_Motor(50,0); // ԭ����ת
			break;

		// ��2+��1+�м�⵽ (00011) - ����ƫ�����������
		case 0b00011:
			Set_Motor(20,50); // �����������ֿ죬�е���ת
			break;

		// ��+��1+��2��⵽ (11000) - ����ƫ�һ���������
		case 0b11000:
			Set_Motor(50,20); // ���ֿ죬���������е���ת
			break;

		// �����м䴫������⵽ (10001) - ���ߣ�����ֱ��
		case 0b10001:
			Motor_Run(80);// ˫��ͬ�٣�����ֱ��
			break;

		// �ĸ���������⵽ - ������ʮ��·�ڻ���߶�
		case 0b10000: // ��1+��+��1+��2 (10000)
			Motor_Run(80); // ˫��ͬ�٣�����ͨ��
			break;

		// ȫ����⵽ (00000) - ʮ��·�ڡ���ʼ�߻��յ���
		case 0b00000:
			Motor_Run(40); // ˫��ͬ�٣�����ͨ��
			break;

		// ֻ�����߼�⵽���м�û�� (01110) - ������ȫ�ѹ�
		case 0b01110:
			Motor_Run(25); // ˫��ͬ�٣�����Ѱ��
			break;

		// ���д�������û��⵽���� (11111) - ��ȫ��ʧ�켣
		case 0b11111:
			Motor_Run(25); // ˫��ͬ�٣�����Ѱ��
			break;

		// ��������������������ļ���ת��
//		default:
//		{
//			// ���㴫��������λ�� (-2��+2��������ʾƫ��������ʾƫ��)
//			int16_t weighted_sum = 0;
//			uint8_t active_sensors = 0;

//			if(sensor_left2 == 0) { weighted_sum += (-2); active_sensors++; }
//			if(sensor_left1 == 0) { weighted_sum += (-1); active_sensors++; }
//			if(sensor_center == 0) { weighted_sum += 0; active_sensors++; }
//			if(sensor_right1 == 0) { weighted_sum += 1; active_sensors++; }
//			if(sensor_right2 == 0) { weighted_sum += 2; active_sensors++; }

//			if(active_sensors > 0)
//			{
//				// ��������λ�ã�����С�����ȣ�
//				int16_t position = weighted_sum * 10 / active_sensors; // ����10����һλС������

//				// �����ٶ�
//				uint8_t base_speed = 60;
//				uint8_t left_speed, right_speed;

//				if(position < -5) { // ��������ƫ����Ҫ��ת
//					left_speed = base_speed - abs(position) * 2;
//					right_speed = base_speed + abs(position);
//					if(left_speed < 10) left_speed = 10;
//					if(right_speed > 90) right_speed = 90;
//				}
//				else if(position > 5) { // ��������ƫ�ң���Ҫ��ת
//					left_speed = base_speed + position * 2;
//					right_speed = base_speed - position;
//					if(left_speed > 90) left_speed = 90;
//					if(right_speed < 10) right_speed = 10;
//				}
//				else { // ���Ľӽ����ģ�ֱ��
//					left_speed = right_speed = base_speed;
//				}

//				Set_Motor(left_speed,right_speed);
//			}
//			else {
//				// û�д�������⵽������Ѱ��
//				Motor_Run(30);
//			}
//			break;
		//}
	}
}

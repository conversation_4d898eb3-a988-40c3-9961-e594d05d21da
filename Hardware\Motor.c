#include "stm32f10x.h"                  // Device header
#include "PWM.h"

// 电机PWM初始化
void Motor_Init(void)
{
	PWM_Init();
}

// 电机驱动函数：Set_Motor(左轮速度, 右轮速度)
// 参数范围：-100到+100，正值前进，负值后退
void Set_Motor(int8_t left_speed, int8_t right_speed)
{
	uint8_t left1 = 0, left2 = 0, right1 = 0, right2 = 0;

	// 限制速度范围
	if(left_speed > 100) left_speed = 100;
	if(left_speed < -100) left_speed = -100;
	if(right_speed > 100) right_speed = 100;
	if(right_speed < -100) right_speed = -100;

	// 左轮控制
	if(left_speed >= 0) 
	{
		left1 = left_speed;  // 正转
		left2 = 0;
	} 
	else 
	{
		left1 = 0;
		left2 = -left_speed; // 反转
	}

	// 右轮控制
	if(right_speed >= 0) 
	{
		right1 = right_speed; // 正转
		right2 = 0;
	} 
	else 
	{
		right1 = 0;
		right2 = -right_speed; // 反转
	}

	// 直接控制PWM输出
	TIM_SetCompare1(TIM4, left1);
	TIM_SetCompare2(TIM4, left2);
	TIM_SetCompare3(TIM4, right1);
	TIM_SetCompare4(TIM4, right2);
}

// 前进 - 双轮同速前进
void Motor_Run(int8_t speed)
{
	Set_Motor(speed, speed);  // 双轮同速前进
}

// 刹车 - 停止所有电机
void Motor_Brake(void)
{
	Set_Motor(0, 0);  // 停止所有电机
}

// 后退 - 双轮同速后退
void Motor_Back(int8_t speed)
{
	Set_Motor(-speed, -speed);  // 双轮同速后退
}

// 左转 - 右轮前进，左轮停止
void Motor_Left(int8_t speed)
{
	Set_Motor(0, speed);  // 左轮停，右轮进
}

// 右转 - 左轮前进，右轮停止
void Motor_Right(int8_t speed)
{
	Set_Motor(speed, 0);  // 左轮进，右轮停
}

// 原地左转 - 左轮后退，右轮前进
void Motor_SpinLeft(int8_t speed)
{
	Set_Motor(-speed, speed);  // 左轮退，右轮进
}

// 原地右转 - 左轮前进，右轮后退
void Motor_SpinRight(int8_t speed)
{
	Set_Motor(speed, -speed);  // 左轮进，右轮退
}

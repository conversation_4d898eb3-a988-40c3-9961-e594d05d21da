#ifndef __MOTOR_H
#define __MOTOR_H

void Motor_Init(void);  // 原robot_Init重命名

// 核心电机驱动函数
void Set_Motor(int8_t left_speed, int8_t right_speed);  // 参数范围：-100到+100

// 运动控制函数
void Motor_Run(int8_t speed);        // 前进（原makerobo_run）
void Motor_Brake(void);              // 刹车（原makerobo_brake）
void Motor_Back(int8_t speed);       // 后退（原makerobo_back）
void Motor_Left(int8_t speed);       // 左转（原makerobo_Left）
void Motor_Right(int8_t speed);      // 右转（原makerobo_Right）
void Motor_SpinLeft(int8_t speed);   // 原地左转（原makerobo_Spin_Left）
void Motor_SpinRight(int8_t speed);  // 原地右转（原makerobo_Spin_Right）

#endif

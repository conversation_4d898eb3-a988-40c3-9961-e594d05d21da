Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to incontrol.o(.text.EXTI9_5_IRQHandler) for EXTI9_5_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to usart1.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(.text.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    core_cm3.o(.ARM.exidx.text.__get_PSP) refers to core_cm3.o(.text.__get_PSP) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_PSP) refers to core_cm3.o(.text.__set_PSP) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_MSP) refers to core_cm3.o(.text.__get_MSP) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_MSP) refers to core_cm3.o(.text.__set_MSP) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_BASEPRI) refers to core_cm3.o(.text.__get_BASEPRI) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_BASEPRI) refers to core_cm3.o(.text.__set_BASEPRI) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_PRIMASK) refers to core_cm3.o(.text.__get_PRIMASK) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_PRIMASK) refers to core_cm3.o(.text.__set_PRIMASK) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_FAULTMASK) refers to core_cm3.o(.text.__get_FAULTMASK) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_FAULTMASK) refers to core_cm3.o(.text.__set_FAULTMASK) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_CONTROL) refers to core_cm3.o(.text.__get_CONTROL) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_CONTROL) refers to core_cm3.o(.text.__set_CONTROL) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__REV) refers to core_cm3.o(.text.__REV) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__REV16) refers to core_cm3.o(.text.__REV16) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__REVSH) refers to core_cm3.o(.text.__REVSH) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__RBIT) refers to core_cm3.o(.text.__RBIT) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__LDREXB) refers to core_cm3.o(.text.__LDREXB) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__LDREXH) refers to core_cm3.o(.text.__LDREXH) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__LDREXW) refers to core_cm3.o(.text.__LDREXW) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__STREXB) refers to core_cm3.o(.text.__STREXB) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__STREXH) refers to core_cm3.o(.text.__STREXH) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__STREXW) refers to core_cm3.o(.text.__STREXW) for [Anonymous Symbol]
    system_stm32f10x.o(.ARM.exidx.text.SystemInit) refers to system_stm32f10x.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f10x.o(.text.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f10x.o(.text.SystemCoreClockUpdate) refers to system_stm32f10x.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f10x.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f10x.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.NVIC_PriorityGroupConfig) refers to misc.o(.text.NVIC_PriorityGroupConfig) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.NVIC_Init) refers to misc.o(.text.NVIC_Init) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.NVIC_SetVectorTable) refers to misc.o(.text.NVIC_SetVectorTable) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.NVIC_SystemLPConfig) refers to misc.o(.text.NVIC_SystemLPConfig) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.SysTick_CLKSourceConfig) refers to misc.o(.text.SysTick_CLKSourceConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.text.ADC_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_adc.o(.ARM.exidx.text.ADC_DeInit) refers to stm32f10x_adc.o(.text.ADC_DeInit) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_Init) refers to stm32f10x_adc.o(.text.ADC_Init) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_StructInit) refers to stm32f10x_adc.o(.text.ADC_StructInit) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_Cmd) refers to stm32f10x_adc.o(.text.ADC_Cmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_DMACmd) refers to stm32f10x_adc.o(.text.ADC_DMACmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ITConfig) refers to stm32f10x_adc.o(.text.ADC_ITConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ResetCalibration) refers to stm32f10x_adc.o(.text.ADC_ResetCalibration) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetResetCalibrationStatus) refers to stm32f10x_adc.o(.text.ADC_GetResetCalibrationStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_StartCalibration) refers to stm32f10x_adc.o(.text.ADC_StartCalibration) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetCalibrationStatus) refers to stm32f10x_adc.o(.text.ADC_GetCalibrationStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_SoftwareStartConvCmd) refers to stm32f10x_adc.o(.text.ADC_SoftwareStartConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetSoftwareStartConvStatus) refers to stm32f10x_adc.o(.text.ADC_GetSoftwareStartConvStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_DiscModeChannelCountConfig) refers to stm32f10x_adc.o(.text.ADC_DiscModeChannelCountConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_DiscModeCmd) refers to stm32f10x_adc.o(.text.ADC_DiscModeCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_RegularChannelConfig) refers to stm32f10x_adc.o(.text.ADC_RegularChannelConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigConvCmd) refers to stm32f10x_adc.o(.text.ADC_ExternalTrigConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetConversionValue) refers to stm32f10x_adc.o(.text.ADC_GetConversionValue) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetDualModeConversionValue) refers to stm32f10x_adc.o(.text.ADC_GetDualModeConversionValue) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_AutoInjectedConvCmd) refers to stm32f10x_adc.o(.text.ADC_AutoInjectedConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedDiscModeCmd) refers to stm32f10x_adc.o(.text.ADC_InjectedDiscModeCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigInjectedConvConfig) refers to stm32f10x_adc.o(.text.ADC_ExternalTrigInjectedConvConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigInjectedConvCmd) refers to stm32f10x_adc.o(.text.ADC_ExternalTrigInjectedConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_SoftwareStartInjectedConvCmd) refers to stm32f10x_adc.o(.text.ADC_SoftwareStartInjectedConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetSoftwareStartInjectedConvCmdStatus) refers to stm32f10x_adc.o(.text.ADC_GetSoftwareStartInjectedConvCmdStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedChannelConfig) refers to stm32f10x_adc.o(.text.ADC_InjectedChannelConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedSequencerLengthConfig) refers to stm32f10x_adc.o(.text.ADC_InjectedSequencerLengthConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_SetInjectedOffset) refers to stm32f10x_adc.o(.text.ADC_SetInjectedOffset) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetInjectedConversionValue) refers to stm32f10x_adc.o(.text.ADC_GetInjectedConversionValue) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogCmd) refers to stm32f10x_adc.o(.text.ADC_AnalogWatchdogCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogThresholdsConfig) refers to stm32f10x_adc.o(.text.ADC_AnalogWatchdogThresholdsConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogSingleChannelConfig) refers to stm32f10x_adc.o(.text.ADC_AnalogWatchdogSingleChannelConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_TempSensorVrefintCmd) refers to stm32f10x_adc.o(.text.ADC_TempSensorVrefintCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetFlagStatus) refers to stm32f10x_adc.o(.text.ADC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ClearFlag) refers to stm32f10x_adc.o(.text.ADC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetITStatus) refers to stm32f10x_adc.o(.text.ADC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ClearITPendingBit) refers to stm32f10x_adc.o(.text.ADC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_bkp.o(.text.BKP_DeInit) refers to stm32f10x_rcc.o(.text.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_DeInit) refers to stm32f10x_bkp.o(.text.BKP_DeInit) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_TamperPinLevelConfig) refers to stm32f10x_bkp.o(.text.BKP_TamperPinLevelConfig) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_TamperPinCmd) refers to stm32f10x_bkp.o(.text.BKP_TamperPinCmd) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_ITConfig) refers to stm32f10x_bkp.o(.text.BKP_ITConfig) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_RTCOutputConfig) refers to stm32f10x_bkp.o(.text.BKP_RTCOutputConfig) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_SetRTCCalibrationValue) refers to stm32f10x_bkp.o(.text.BKP_SetRTCCalibrationValue) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_WriteBackupRegister) refers to stm32f10x_bkp.o(.text.BKP_WriteBackupRegister) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_ReadBackupRegister) refers to stm32f10x_bkp.o(.text.BKP_ReadBackupRegister) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_GetFlagStatus) refers to stm32f10x_bkp.o(.text.BKP_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_ClearFlag) refers to stm32f10x_bkp.o(.text.BKP_ClearFlag) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_GetITStatus) refers to stm32f10x_bkp.o(.text.BKP_GetITStatus) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_ClearITPendingBit) refers to stm32f10x_bkp.o(.text.BKP_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_can.o(.text.CAN_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(.ARM.exidx.text.CAN_DeInit) refers to stm32f10x_can.o(.text.CAN_DeInit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_Init) refers to stm32f10x_can.o(.text.CAN_Init) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_FilterInit) refers to stm32f10x_can.o(.text.CAN_FilterInit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_StructInit) refers to stm32f10x_can.o(.text.CAN_StructInit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_SlaveStartBank) refers to stm32f10x_can.o(.text.CAN_SlaveStartBank) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_DBGFreeze) refers to stm32f10x_can.o(.text.CAN_DBGFreeze) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_TTComModeCmd) refers to stm32f10x_can.o(.text.CAN_TTComModeCmd) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_Transmit) refers to stm32f10x_can.o(.text.CAN_Transmit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_TransmitStatus) refers to stm32f10x_can.o(.text.CAN_TransmitStatus) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_CancelTransmit) refers to stm32f10x_can.o(.text.CAN_CancelTransmit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_Receive) refers to stm32f10x_can.o(.text.CAN_Receive) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_FIFORelease) refers to stm32f10x_can.o(.text.CAN_FIFORelease) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_MessagePending) refers to stm32f10x_can.o(.text.CAN_MessagePending) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_OperatingModeRequest) refers to stm32f10x_can.o(.text.CAN_OperatingModeRequest) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_Sleep) refers to stm32f10x_can.o(.text.CAN_Sleep) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_WakeUp) refers to stm32f10x_can.o(.text.CAN_WakeUp) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetLastErrorCode) refers to stm32f10x_can.o(.text.CAN_GetLastErrorCode) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetReceiveErrorCounter) refers to stm32f10x_can.o(.text.CAN_GetReceiveErrorCounter) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetLSBTransmitErrorCounter) refers to stm32f10x_can.o(.text.CAN_GetLSBTransmitErrorCounter) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_ITConfig) refers to stm32f10x_can.o(.text.CAN_ITConfig) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetFlagStatus) refers to stm32f10x_can.o(.text.CAN_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_ClearFlag) refers to stm32f10x_can.o(.text.CAN_ClearFlag) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetITStatus) refers to stm32f10x_can.o(.text.CAN_GetITStatus) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_ClearITPendingBit) refers to stm32f10x_can.o(.text.CAN_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_cec.o(.text.CEC_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_cec.o(.ARM.exidx.text.CEC_DeInit) refers to stm32f10x_cec.o(.text.CEC_DeInit) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_Init) refers to stm32f10x_cec.o(.text.CEC_Init) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_Cmd) refers to stm32f10x_cec.o(.text.CEC_Cmd) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_ITConfig) refers to stm32f10x_cec.o(.text.CEC_ITConfig) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_OwnAddressConfig) refers to stm32f10x_cec.o(.text.CEC_OwnAddressConfig) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_SetPrescaler) refers to stm32f10x_cec.o(.text.CEC_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_SendDataByte) refers to stm32f10x_cec.o(.text.CEC_SendDataByte) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_ReceiveDataByte) refers to stm32f10x_cec.o(.text.CEC_ReceiveDataByte) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_StartOfMessage) refers to stm32f10x_cec.o(.text.CEC_StartOfMessage) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_EndOfMessageCmd) refers to stm32f10x_cec.o(.text.CEC_EndOfMessageCmd) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_GetFlagStatus) refers to stm32f10x_cec.o(.text.CEC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_ClearFlag) refers to stm32f10x_cec.o(.text.CEC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_GetITStatus) refers to stm32f10x_cec.o(.text.CEC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_ClearITPendingBit) refers to stm32f10x_cec.o(.text.CEC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_ResetDR) refers to stm32f10x_crc.o(.text.CRC_ResetDR) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_CalcCRC) refers to stm32f10x_crc.o(.text.CRC_CalcCRC) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_CalcBlockCRC) refers to stm32f10x_crc.o(.text.CRC_CalcBlockCRC) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_GetCRC) refers to stm32f10x_crc.o(.text.CRC_GetCRC) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_SetIDRegister) refers to stm32f10x_crc.o(.text.CRC_SetIDRegister) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_GetIDRegister) refers to stm32f10x_crc.o(.text.CRC_GetIDRegister) for [Anonymous Symbol]
    stm32f10x_dac.o(.text.DAC_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(.ARM.exidx.text.DAC_DeInit) refers to stm32f10x_dac.o(.text.DAC_DeInit) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_Init) refers to stm32f10x_dac.o(.text.DAC_Init) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_StructInit) refers to stm32f10x_dac.o(.text.DAC_StructInit) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_Cmd) refers to stm32f10x_dac.o(.text.DAC_Cmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_DMACmd) refers to stm32f10x_dac.o(.text.DAC_DMACmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_SoftwareTriggerCmd) refers to stm32f10x_dac.o(.text.DAC_SoftwareTriggerCmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_DualSoftwareTriggerCmd) refers to stm32f10x_dac.o(.text.DAC_DualSoftwareTriggerCmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_WaveGenerationCmd) refers to stm32f10x_dac.o(.text.DAC_WaveGenerationCmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_SetChannel1Data) refers to stm32f10x_dac.o(.text.DAC_SetChannel1Data) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_SetChannel2Data) refers to stm32f10x_dac.o(.text.DAC_SetChannel2Data) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_SetDualChannelData) refers to stm32f10x_dac.o(.text.DAC_SetDualChannelData) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_GetDataOutputValue) refers to stm32f10x_dac.o(.text.DAC_GetDataOutputValue) for [Anonymous Symbol]
    stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_GetREVID) refers to stm32f10x_dbgmcu.o(.text.DBGMCU_GetREVID) for [Anonymous Symbol]
    stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_GetDEVID) refers to stm32f10x_dbgmcu.o(.text.DBGMCU_GetDEVID) for [Anonymous Symbol]
    stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_Config) refers to stm32f10x_dbgmcu.o(.text.DBGMCU_Config) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_DeInit) refers to stm32f10x_dma.o(.text.DMA_DeInit) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_Init) refers to stm32f10x_dma.o(.text.DMA_Init) for [Anonymous Symbol]
    stm32f10x_dma.o(.text.DMA_StructInit) refers to memseta.o(.text) for __aeabi_memclr4
    stm32f10x_dma.o(.ARM.exidx.text.DMA_StructInit) refers to stm32f10x_dma.o(.text.DMA_StructInit) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_Cmd) refers to stm32f10x_dma.o(.text.DMA_Cmd) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_ITConfig) refers to stm32f10x_dma.o(.text.DMA_ITConfig) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_SetCurrDataCounter) refers to stm32f10x_dma.o(.text.DMA_SetCurrDataCounter) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_GetCurrDataCounter) refers to stm32f10x_dma.o(.text.DMA_GetCurrDataCounter) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_GetFlagStatus) refers to stm32f10x_dma.o(.text.DMA_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_ClearFlag) refers to stm32f10x_dma.o(.text.DMA_ClearFlag) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_GetITStatus) refers to stm32f10x_dma.o(.text.DMA_GetITStatus) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_ClearITPendingBit) refers to stm32f10x_dma.o(.text.DMA_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_DeInit) refers to stm32f10x_exti.o(.text.EXTI_DeInit) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_Init) refers to stm32f10x_exti.o(.text.EXTI_Init) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_StructInit) refers to stm32f10x_exti.o(.text.EXTI_StructInit) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_GenerateSWInterrupt) refers to stm32f10x_exti.o(.text.EXTI_GenerateSWInterrupt) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_GetFlagStatus) refers to stm32f10x_exti.o(.text.EXTI_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_ClearFlag) refers to stm32f10x_exti.o(.text.EXTI_ClearFlag) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_GetITStatus) refers to stm32f10x_exti.o(.text.EXTI_GetITStatus) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_ClearITPendingBit) refers to stm32f10x_exti.o(.text.EXTI_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_SetLatency) refers to stm32f10x_flash.o(.text.FLASH_SetLatency) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_HalfCycleAccessCmd) refers to stm32f10x_flash.o(.text.FLASH_HalfCycleAccessCmd) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_PrefetchBufferCmd) refers to stm32f10x_flash.o(.text.FLASH_PrefetchBufferCmd) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_Unlock) refers to stm32f10x_flash.o(.text.FLASH_Unlock) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_UnlockBank1) refers to stm32f10x_flash.o(.text.FLASH_UnlockBank1) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_Lock) refers to stm32f10x_flash.o(.text.FLASH_Lock) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_LockBank1) refers to stm32f10x_flash.o(.text.FLASH_LockBank1) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ErasePage) refers to stm32f10x_flash.o(.text.FLASH_ErasePage) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseAllPages) refers to stm32f10x_flash.o(.text.FLASH_EraseAllPages) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(.text.FLASH_EraseAllBank1Pages) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(.text.FLASH_WaitForLastBank1Operation) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(.text.FLASH_EraseOptionBytes) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetReadOutProtectionStatus) refers to stm32f10x_flash.o(.text.FLASH_GetReadOutProtectionStatus) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramWord) refers to stm32f10x_flash.o(.text.FLASH_ProgramWord) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(.text.FLASH_ProgramHalfWord) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(.text.FLASH_ProgramOptionByteData) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(.text.FLASH_EnableWriteProtection) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(.text.FLASH_ReadOutProtection) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(.text.FLASH_UserOptionByteConfig) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetUserOptionByte) refers to stm32f10x_flash.o(.text.FLASH_GetUserOptionByte) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetWriteProtectionOptionByte) refers to stm32f10x_flash.o(.text.FLASH_GetWriteProtectionOptionByte) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetPrefetchBufferStatus) refers to stm32f10x_flash.o(.text.FLASH_GetPrefetchBufferStatus) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ITConfig) refers to stm32f10x_flash.o(.text.FLASH_ITConfig) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetFlagStatus) refers to stm32f10x_flash.o(.text.FLASH_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ClearFlag) refers to stm32f10x_flash.o(.text.FLASH_ClearFlag) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetStatus) refers to stm32f10x_flash.o(.text.FLASH_GetStatus) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetBank1Status) refers to stm32f10x_flash.o(.text.FLASH_GetBank1Status) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMDeInit) refers to stm32f10x_fsmc.o(.text.FSMC_NORSRAMDeInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDDeInit) refers to stm32f10x_fsmc.o(.text.FSMC_NANDDeInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDDeInit) refers to stm32f10x_fsmc.o(.text.FSMC_PCCARDDeInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMInit) refers to stm32f10x_fsmc.o(.text.FSMC_NORSRAMInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDInit) refers to stm32f10x_fsmc.o(.text.FSMC_NANDInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDInit) refers to stm32f10x_fsmc.o(.text.FSMC_PCCARDInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMStructInit) refers to stm32f10x_fsmc.o(.text.FSMC_NORSRAMStructInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDStructInit) refers to stm32f10x_fsmc.o(.text.FSMC_NANDStructInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDStructInit) refers to stm32f10x_fsmc.o(.text.FSMC_PCCARDStructInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMCmd) refers to stm32f10x_fsmc.o(.text.FSMC_NORSRAMCmd) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDCmd) refers to stm32f10x_fsmc.o(.text.FSMC_NANDCmd) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDCmd) refers to stm32f10x_fsmc.o(.text.FSMC_PCCARDCmd) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDECCCmd) refers to stm32f10x_fsmc.o(.text.FSMC_NANDECCCmd) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetECC) refers to stm32f10x_fsmc.o(.text.FSMC_GetECC) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ITConfig) refers to stm32f10x_fsmc.o(.text.FSMC_ITConfig) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetFlagStatus) refers to stm32f10x_fsmc.o(.text.FSMC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ClearFlag) refers to stm32f10x_fsmc.o(.text.FSMC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetITStatus) refers to stm32f10x_fsmc.o(.text.FSMC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ClearITPendingBit) refers to stm32f10x_fsmc.o(.text.FSMC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.text.GPIO_DeInit) refers to stm32f10x_gpio.o(.rodata..Lswitch.table.GPIO_DeInit.1) for .Lswitch.table.GPIO_DeInit.1
    stm32f10x_gpio.o(.text.GPIO_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_DeInit) refers to stm32f10x_gpio.o(.text.GPIO_DeInit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.text.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_AFIODeInit) refers to stm32f10x_gpio.o(.text.GPIO_AFIODeInit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_StructInit) refers to stm32f10x_gpio.o(.text.GPIO_StructInit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadInputDataBit) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadInputData) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputData) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadOutputDataBit) refers to stm32f10x_gpio.o(.text.GPIO_ReadOutputDataBit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadOutputData) refers to stm32f10x_gpio.o(.text.GPIO_ReadOutputData) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_SetBits) refers to stm32f10x_gpio.o(.text.GPIO_SetBits) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ResetBits) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_WriteBit) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_Write) refers to stm32f10x_gpio.o(.text.GPIO_Write) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_PinLockConfig) refers to stm32f10x_gpio.o(.text.GPIO_PinLockConfig) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EventOutputConfig) refers to stm32f10x_gpio.o(.text.GPIO_EventOutputConfig) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EventOutputCmd) refers to stm32f10x_gpio.o(.text.GPIO_EventOutputCmd) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_PinRemapConfig) refers to stm32f10x_gpio.o(.text.GPIO_PinRemapConfig) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EXTILineConfig) refers to stm32f10x_gpio.o(.text.GPIO_EXTILineConfig) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ETH_MediaInterfaceConfig) refers to stm32f10x_gpio.o(.text.GPIO_ETH_MediaInterfaceConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.text.I2C_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_DeInit) refers to stm32f10x_i2c.o(.text.I2C_DeInit) for [Anonymous Symbol]
    stm32f10x_i2c.o(.text.I2C_Init) refers to stm32f10x_rcc.o(.text.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_Init) refers to stm32f10x_i2c.o(.text.I2C_Init) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_StructInit) refers to stm32f10x_i2c.o(.text.I2C_StructInit) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_Cmd) refers to stm32f10x_i2c.o(.text.I2C_Cmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_DMACmd) refers to stm32f10x_i2c.o(.text.I2C_DMACmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_DMALastTransferCmd) refers to stm32f10x_i2c.o(.text.I2C_DMALastTransferCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GenerateSTART) refers to stm32f10x_i2c.o(.text.I2C_GenerateSTART) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GenerateSTOP) refers to stm32f10x_i2c.o(.text.I2C_GenerateSTOP) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_AcknowledgeConfig) refers to stm32f10x_i2c.o(.text.I2C_AcknowledgeConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_OwnAddress2Config) refers to stm32f10x_i2c.o(.text.I2C_OwnAddress2Config) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_DualAddressCmd) refers to stm32f10x_i2c.o(.text.I2C_DualAddressCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GeneralCallCmd) refers to stm32f10x_i2c.o(.text.I2C_GeneralCallCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ITConfig) refers to stm32f10x_i2c.o(.text.I2C_ITConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_SendData) refers to stm32f10x_i2c.o(.text.I2C_SendData) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ReceiveData) refers to stm32f10x_i2c.o(.text.I2C_ReceiveData) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_Send7bitAddress) refers to stm32f10x_i2c.o(.text.I2C_Send7bitAddress) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ReadRegister) refers to stm32f10x_i2c.o(.text.I2C_ReadRegister) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_SoftwareResetCmd) refers to stm32f10x_i2c.o(.text.I2C_SoftwareResetCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_NACKPositionConfig) refers to stm32f10x_i2c.o(.text.I2C_NACKPositionConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_SMBusAlertConfig) refers to stm32f10x_i2c.o(.text.I2C_SMBusAlertConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_TransmitPEC) refers to stm32f10x_i2c.o(.text.I2C_TransmitPEC) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_PECPositionConfig) refers to stm32f10x_i2c.o(.text.I2C_PECPositionConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_CalculatePEC) refers to stm32f10x_i2c.o(.text.I2C_CalculatePEC) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetPEC) refers to stm32f10x_i2c.o(.text.I2C_GetPEC) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ARPCmd) refers to stm32f10x_i2c.o(.text.I2C_ARPCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_StretchClockCmd) refers to stm32f10x_i2c.o(.text.I2C_StretchClockCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_FastModeDutyCycleConfig) refers to stm32f10x_i2c.o(.text.I2C_FastModeDutyCycleConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_CheckEvent) refers to stm32f10x_i2c.o(.text.I2C_CheckEvent) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetLastEvent) refers to stm32f10x_i2c.o(.text.I2C_GetLastEvent) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetFlagStatus) refers to stm32f10x_i2c.o(.text.I2C_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ClearFlag) refers to stm32f10x_i2c.o(.text.I2C_ClearFlag) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetITStatus) refers to stm32f10x_i2c.o(.text.I2C_GetITStatus) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ClearITPendingBit) refers to stm32f10x_i2c.o(.text.I2C_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_WriteAccessCmd) refers to stm32f10x_iwdg.o(.text.IWDG_WriteAccessCmd) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_SetPrescaler) refers to stm32f10x_iwdg.o(.text.IWDG_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_SetReload) refers to stm32f10x_iwdg.o(.text.IWDG_SetReload) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_ReloadCounter) refers to stm32f10x_iwdg.o(.text.IWDG_ReloadCounter) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_Enable) refers to stm32f10x_iwdg.o(.text.IWDG_Enable) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_GetFlagStatus) refers to stm32f10x_iwdg.o(.text.IWDG_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_pwr.o(.text.PWR_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_DeInit) refers to stm32f10x_pwr.o(.text.PWR_DeInit) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_BackupAccessCmd) refers to stm32f10x_pwr.o(.text.PWR_BackupAccessCmd) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_PVDCmd) refers to stm32f10x_pwr.o(.text.PWR_PVDCmd) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_PVDLevelConfig) refers to stm32f10x_pwr.o(.text.PWR_PVDLevelConfig) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_WakeUpPinCmd) refers to stm32f10x_pwr.o(.text.PWR_WakeUpPinCmd) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_EnterSTOPMode) refers to stm32f10x_pwr.o(.text.PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_EnterSTANDBYMode) refers to stm32f10x_pwr.o(.text.PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_GetFlagStatus) refers to stm32f10x_pwr.o(.text.PWR_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_ClearFlag) refers to stm32f10x_pwr.o(.text.PWR_ClearFlag) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_DeInit) refers to stm32f10x_rcc.o(.text.RCC_DeInit) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_HSEConfig) refers to stm32f10x_rcc.o(.text.RCC_HSEConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(.text.RCC_WaitForHSEStartUp) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetFlagStatus) refers to stm32f10x_rcc.o(.text.RCC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_AdjustHSICalibrationValue) refers to stm32f10x_rcc.o(.text.RCC_AdjustHSICalibrationValue) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_HSICmd) refers to stm32f10x_rcc.o(.text.RCC_HSICmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_PLLConfig) refers to stm32f10x_rcc.o(.text.RCC_PLLConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_PLLCmd) refers to stm32f10x_rcc.o(.text.RCC_PLLCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_SYSCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_SYSCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetSYSCLKSource) refers to stm32f10x_rcc.o(.text.RCC_GetSYSCLKSource) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_HCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_HCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_PCLK1Config) refers to stm32f10x_rcc.o(.text.RCC_PCLK1Config) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_PCLK2Config) refers to stm32f10x_rcc.o(.text.RCC_PCLK2Config) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ITConfig) refers to stm32f10x_rcc.o(.text.RCC_ITConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_USBCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_USBCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ADCCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_ADCCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_LSEConfig) refers to stm32f10x_rcc.o(.text.RCC_LSEConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_LSICmd) refers to stm32f10x_rcc.o(.text.RCC_LSICmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_RTCCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_RTCCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_RTCCLKCmd) refers to stm32f10x_rcc.o(.text.RCC_RTCCLKCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.text.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.rodata.APBAHBPrescTable) for APBAHBPrescTable
    stm32f10x_rcc.o(.text.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.rodata.ADCPrescTable) for ADCPrescTable
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.text.RCC_GetClocksFreq) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_AHBPeriphClockCmd) refers to stm32f10x_rcc.o(.text.RCC_AHBPeriphClockCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB2PeriphClockCmd) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB1PeriphClockCmd) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB2PeriphResetCmd) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB1PeriphResetCmd) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_BackupResetCmd) refers to stm32f10x_rcc.o(.text.RCC_BackupResetCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClockSecuritySystemCmd) refers to stm32f10x_rcc.o(.text.RCC_ClockSecuritySystemCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_MCOConfig) refers to stm32f10x_rcc.o(.text.RCC_MCOConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClearFlag) refers to stm32f10x_rcc.o(.text.RCC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetITStatus) refers to stm32f10x_rcc.o(.text.RCC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClearITPendingBit) refers to stm32f10x_rcc.o(.text.RCC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_ITConfig) refers to stm32f10x_rtc.o(.text.RTC_ITConfig) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_EnterConfigMode) refers to stm32f10x_rtc.o(.text.RTC_EnterConfigMode) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_ExitConfigMode) refers to stm32f10x_rtc.o(.text.RTC_ExitConfigMode) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetCounter) refers to stm32f10x_rtc.o(.text.RTC_GetCounter) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetCounter) refers to stm32f10x_rtc.o(.text.RTC_SetCounter) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetPrescaler) refers to stm32f10x_rtc.o(.text.RTC_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetAlarm) refers to stm32f10x_rtc.o(.text.RTC_SetAlarm) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetDivider) refers to stm32f10x_rtc.o(.text.RTC_GetDivider) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_WaitForLastTask) refers to stm32f10x_rtc.o(.text.RTC_WaitForLastTask) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_WaitForSynchro) refers to stm32f10x_rtc.o(.text.RTC_WaitForSynchro) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetFlagStatus) refers to stm32f10x_rtc.o(.text.RTC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_ClearFlag) refers to stm32f10x_rtc.o(.text.RTC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetITStatus) refers to stm32f10x_rtc.o(.text.RTC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_ClearITPendingBit) refers to stm32f10x_rtc.o(.text.RTC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DeInit) refers to stm32f10x_sdio.o(.text.SDIO_DeInit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_Init) refers to stm32f10x_sdio.o(.text.SDIO_Init) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StructInit) refers to stm32f10x_sdio.o(.text.SDIO_StructInit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClockCmd) refers to stm32f10x_sdio.o(.text.SDIO_ClockCmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetPowerState) refers to stm32f10x_sdio.o(.text.SDIO_SetPowerState) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetPowerState) refers to stm32f10x_sdio.o(.text.SDIO_GetPowerState) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ITConfig) refers to stm32f10x_sdio.o(.text.SDIO_ITConfig) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DMACmd) refers to stm32f10x_sdio.o(.text.SDIO_DMACmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendCommand) refers to stm32f10x_sdio.o(.text.SDIO_SendCommand) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CmdStructInit) refers to stm32f10x_sdio.o(.text.SDIO_CmdStructInit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetCommandResponse) refers to stm32f10x_sdio.o(.text.SDIO_GetCommandResponse) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetResponse) refers to stm32f10x_sdio.o(.text.SDIO_GetResponse) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DataConfig) refers to stm32f10x_sdio.o(.text.SDIO_DataConfig) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DataStructInit) refers to stm32f10x_sdio.o(.text.SDIO_DataStructInit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetDataCounter) refers to stm32f10x_sdio.o(.text.SDIO_GetDataCounter) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ReadData) refers to stm32f10x_sdio.o(.text.SDIO_ReadData) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_WriteData) refers to stm32f10x_sdio.o(.text.SDIO_WriteData) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetFIFOCount) refers to stm32f10x_sdio.o(.text.SDIO_GetFIFOCount) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StartSDIOReadWait) refers to stm32f10x_sdio.o(.text.SDIO_StartSDIOReadWait) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StopSDIOReadWait) refers to stm32f10x_sdio.o(.text.SDIO_StopSDIOReadWait) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetSDIOReadWaitMode) refers to stm32f10x_sdio.o(.text.SDIO_SetSDIOReadWaitMode) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetSDIOOperation) refers to stm32f10x_sdio.o(.text.SDIO_SetSDIOOperation) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendSDIOSuspendCmd) refers to stm32f10x_sdio.o(.text.SDIO_SendSDIOSuspendCmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CommandCompletionCmd) refers to stm32f10x_sdio.o(.text.SDIO_CommandCompletionCmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CEATAITCmd) refers to stm32f10x_sdio.o(.text.SDIO_CEATAITCmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendCEATACmd) refers to stm32f10x_sdio.o(.text.SDIO_SendCEATACmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetFlagStatus) refers to stm32f10x_sdio.o(.text.SDIO_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClearFlag) refers to stm32f10x_sdio.o(.text.SDIO_ClearFlag) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetITStatus) refers to stm32f10x_sdio.o(.text.SDIO_GetITStatus) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClearITPendingBit) refers to stm32f10x_sdio.o(.text.SDIO_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_spi.o(.text.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(.text.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_DeInit) refers to stm32f10x_spi.o(.text.SPI_I2S_DeInit) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_Init) refers to stm32f10x_spi.o(.text.SPI_Init) for [Anonymous Symbol]
    stm32f10x_spi.o(.text.I2S_Init) refers to stm32f10x_rcc.o(.text.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(.ARM.exidx.text.I2S_Init) refers to stm32f10x_spi.o(.text.I2S_Init) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_StructInit) refers to stm32f10x_spi.o(.text.SPI_StructInit) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.I2S_StructInit) refers to stm32f10x_spi.o(.text.I2S_StructInit) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_Cmd) refers to stm32f10x_spi.o(.text.SPI_Cmd) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.I2S_Cmd) refers to stm32f10x_spi.o(.text.I2S_Cmd) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ITConfig) refers to stm32f10x_spi.o(.text.SPI_I2S_ITConfig) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_DMACmd) refers to stm32f10x_spi.o(.text.SPI_I2S_DMACmd) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_SendData) refers to stm32f10x_spi.o(.text.SPI_I2S_SendData) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ReceiveData) refers to stm32f10x_spi.o(.text.SPI_I2S_ReceiveData) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_NSSInternalSoftwareConfig) refers to stm32f10x_spi.o(.text.SPI_NSSInternalSoftwareConfig) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_SSOutputCmd) refers to stm32f10x_spi.o(.text.SPI_SSOutputCmd) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_DataSizeConfig) refers to stm32f10x_spi.o(.text.SPI_DataSizeConfig) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_TransmitCRC) refers to stm32f10x_spi.o(.text.SPI_TransmitCRC) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_CalculateCRC) refers to stm32f10x_spi.o(.text.SPI_CalculateCRC) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_GetCRC) refers to stm32f10x_spi.o(.text.SPI_GetCRC) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_GetCRCPolynomial) refers to stm32f10x_spi.o(.text.SPI_GetCRCPolynomial) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_BiDirectionalLineConfig) refers to stm32f10x_spi.o(.text.SPI_BiDirectionalLineConfig) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_GetFlagStatus) refers to stm32f10x_spi.o(.text.SPI_I2S_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ClearFlag) refers to stm32f10x_spi.o(.text.SPI_I2S_ClearFlag) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_GetITStatus) refers to stm32f10x_spi.o(.text.SPI_I2S_GetITStatus) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ClearITPendingBit) refers to stm32f10x_spi.o(.text.SPI_I2S_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_tim.o(.text.TIM_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(.text.TIM_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(.ARM.exidx.text.TIM_DeInit) refers to stm32f10x_tim.o(.text.TIM_DeInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_TimeBaseInit) refers to stm32f10x_tim.o(.text.TIM_TimeBaseInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1Init) refers to stm32f10x_tim.o(.text.TIM_OC1Init) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2Init) refers to stm32f10x_tim.o(.text.TIM_OC2Init) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3Init) refers to stm32f10x_tim.o(.text.TIM_OC3Init) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4Init) refers to stm32f10x_tim.o(.text.TIM_OC4Init) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ICInit) refers to stm32f10x_tim.o(.text.TIM_ICInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC1Prescaler) refers to stm32f10x_tim.o(.text.TIM_SetIC1Prescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC2Prescaler) refers to stm32f10x_tim.o(.text.TIM_SetIC2Prescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC3Prescaler) refers to stm32f10x_tim.o(.text.TIM_SetIC3Prescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC4Prescaler) refers to stm32f10x_tim.o(.text.TIM_SetIC4Prescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_PWMIConfig) refers to stm32f10x_tim.o(.text.TIM_PWMIConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_BDTRConfig) refers to stm32f10x_tim.o(.text.TIM_BDTRConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_TimeBaseStructInit) refers to stm32f10x_tim.o(.text.TIM_TimeBaseStructInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OCStructInit) refers to stm32f10x_tim.o(.text.TIM_OCStructInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ICStructInit) refers to stm32f10x_tim.o(.text.TIM_ICStructInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_BDTRStructInit) refers to stm32f10x_tim.o(.text.TIM_BDTRStructInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_Cmd) refers to stm32f10x_tim.o(.text.TIM_Cmd) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CtrlPWMOutputs) refers to stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ITConfig) refers to stm32f10x_tim.o(.text.TIM_ITConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GenerateEvent) refers to stm32f10x_tim.o(.text.TIM_GenerateEvent) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_DMAConfig) refers to stm32f10x_tim.o(.text.TIM_DMAConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_DMACmd) refers to stm32f10x_tim.o(.text.TIM_DMACmd) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_InternalClockConfig) refers to stm32f10x_tim.o(.text.TIM_InternalClockConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(.text.TIM_ITRxExternalClockConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectInputTrigger) refers to stm32f10x_tim.o(.text.TIM_SelectInputTrigger) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(.text.TIM_TIxExternalClockConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(.text.TIM_ETRClockMode1Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRConfig) refers to stm32f10x_tim.o(.text.TIM_ETRConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(.text.TIM_ETRClockMode2Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_PrescalerConfig) refers to stm32f10x_tim.o(.text.TIM_PrescalerConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CounterModeConfig) refers to stm32f10x_tim.o(.text.TIM_CounterModeConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_EncoderInterfaceConfig) refers to stm32f10x_tim.o(.text.TIM_EncoderInterfaceConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC1Config) refers to stm32f10x_tim.o(.text.TIM_ForcedOC1Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC2Config) refers to stm32f10x_tim.o(.text.TIM_ForcedOC2Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC3Config) refers to stm32f10x_tim.o(.text.TIM_ForcedOC3Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC4Config) refers to stm32f10x_tim.o(.text.TIM_ForcedOC4Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ARRPreloadConfig) refers to stm32f10x_tim.o(.text.TIM_ARRPreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectCOM) refers to stm32f10x_tim.o(.text.TIM_SelectCOM) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectCCDMA) refers to stm32f10x_tim.o(.text.TIM_SelectCCDMA) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CCPreloadControl) refers to stm32f10x_tim.o(.text.TIM_CCPreloadControl) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1PreloadConfig) refers to stm32f10x_tim.o(.text.TIM_OC1PreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2PreloadConfig) refers to stm32f10x_tim.o(.text.TIM_OC2PreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3PreloadConfig) refers to stm32f10x_tim.o(.text.TIM_OC3PreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4PreloadConfig) refers to stm32f10x_tim.o(.text.TIM_OC4PreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1FastConfig) refers to stm32f10x_tim.o(.text.TIM_OC1FastConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2FastConfig) refers to stm32f10x_tim.o(.text.TIM_OC2FastConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3FastConfig) refers to stm32f10x_tim.o(.text.TIM_OC3FastConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4FastConfig) refers to stm32f10x_tim.o(.text.TIM_OC4FastConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC1Ref) refers to stm32f10x_tim.o(.text.TIM_ClearOC1Ref) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC2Ref) refers to stm32f10x_tim.o(.text.TIM_ClearOC2Ref) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC3Ref) refers to stm32f10x_tim.o(.text.TIM_ClearOC3Ref) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC4Ref) refers to stm32f10x_tim.o(.text.TIM_ClearOC4Ref) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1PolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC1PolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1NPolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC1NPolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2PolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC2PolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2NPolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC2NPolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3PolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC3PolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3NPolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC3NPolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4PolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC4PolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CCxCmd) refers to stm32f10x_tim.o(.text.TIM_CCxCmd) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CCxNCmd) refers to stm32f10x_tim.o(.text.TIM_CCxNCmd) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOCxM) refers to stm32f10x_tim.o(.text.TIM_SelectOCxM) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_UpdateDisableConfig) refers to stm32f10x_tim.o(.text.TIM_UpdateDisableConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_UpdateRequestConfig) refers to stm32f10x_tim.o(.text.TIM_UpdateRequestConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectHallSensor) refers to stm32f10x_tim.o(.text.TIM_SelectHallSensor) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOnePulseMode) refers to stm32f10x_tim.o(.text.TIM_SelectOnePulseMode) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOutputTrigger) refers to stm32f10x_tim.o(.text.TIM_SelectOutputTrigger) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectSlaveMode) refers to stm32f10x_tim.o(.text.TIM_SelectSlaveMode) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectMasterSlaveMode) refers to stm32f10x_tim.o(.text.TIM_SelectMasterSlaveMode) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCounter) refers to stm32f10x_tim.o(.text.TIM_SetCounter) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetAutoreload) refers to stm32f10x_tim.o(.text.TIM_SetAutoreload) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare1) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare2) refers to stm32f10x_tim.o(.text.TIM_SetCompare2) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare3) refers to stm32f10x_tim.o(.text.TIM_SetCompare3) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare4) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetClockDivision) refers to stm32f10x_tim.o(.text.TIM_SetClockDivision) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture1) refers to stm32f10x_tim.o(.text.TIM_GetCapture1) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture2) refers to stm32f10x_tim.o(.text.TIM_GetCapture2) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture3) refers to stm32f10x_tim.o(.text.TIM_GetCapture3) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture4) refers to stm32f10x_tim.o(.text.TIM_GetCapture4) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCounter) refers to stm32f10x_tim.o(.text.TIM_GetCounter) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetPrescaler) refers to stm32f10x_tim.o(.text.TIM_GetPrescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetFlagStatus) refers to stm32f10x_tim.o(.text.TIM_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearFlag) refers to stm32f10x_tim.o(.text.TIM_ClearFlag) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetITStatus) refers to stm32f10x_tim.o(.text.TIM_GetITStatus) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearITPendingBit) refers to stm32f10x_tim.o(.text.TIM_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_usart.o(.text.USART_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(.text.USART_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(.ARM.exidx.text.USART_DeInit) refers to stm32f10x_usart.o(.text.USART_DeInit) for [Anonymous Symbol]
    stm32f10x_usart.o(.text.USART_Init) refers to stm32f10x_rcc.o(.text.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_usart.o(.ARM.exidx.text.USART_Init) refers to stm32f10x_usart.o(.text.USART_Init) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_StructInit) refers to stm32f10x_usart.o(.text.USART_StructInit) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ClockInit) refers to stm32f10x_usart.o(.text.USART_ClockInit) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ClockStructInit) refers to stm32f10x_usart.o(.text.USART_ClockStructInit) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_Cmd) refers to stm32f10x_usart.o(.text.USART_Cmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ITConfig) refers to stm32f10x_usart.o(.text.USART_ITConfig) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_DMACmd) refers to stm32f10x_usart.o(.text.USART_DMACmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SetAddress) refers to stm32f10x_usart.o(.text.USART_SetAddress) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_WakeUpConfig) refers to stm32f10x_usart.o(.text.USART_WakeUpConfig) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ReceiverWakeUpCmd) refers to stm32f10x_usart.o(.text.USART_ReceiverWakeUpCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_LINBreakDetectLengthConfig) refers to stm32f10x_usart.o(.text.USART_LINBreakDetectLengthConfig) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_LINCmd) refers to stm32f10x_usart.o(.text.USART_LINCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SendData) refers to stm32f10x_usart.o(.text.USART_SendData) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ReceiveData) refers to stm32f10x_usart.o(.text.USART_ReceiveData) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SendBreak) refers to stm32f10x_usart.o(.text.USART_SendBreak) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SetGuardTime) refers to stm32f10x_usart.o(.text.USART_SetGuardTime) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SetPrescaler) refers to stm32f10x_usart.o(.text.USART_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SmartCardCmd) refers to stm32f10x_usart.o(.text.USART_SmartCardCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SmartCardNACKCmd) refers to stm32f10x_usart.o(.text.USART_SmartCardNACKCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_HalfDuplexCmd) refers to stm32f10x_usart.o(.text.USART_HalfDuplexCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_OverSampling8Cmd) refers to stm32f10x_usart.o(.text.USART_OverSampling8Cmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_OneBitMethodCmd) refers to stm32f10x_usart.o(.text.USART_OneBitMethodCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_IrDAConfig) refers to stm32f10x_usart.o(.text.USART_IrDAConfig) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_IrDACmd) refers to stm32f10x_usart.o(.text.USART_IrDACmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_GetFlagStatus) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ClearFlag) refers to stm32f10x_usart.o(.text.USART_ClearFlag) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_GetITStatus) refers to stm32f10x_usart.o(.text.USART_GetITStatus) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ClearITPendingBit) refers to stm32f10x_usart.o(.text.USART_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.text.WWDG_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_DeInit) refers to stm32f10x_wwdg.o(.text.WWDG_DeInit) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetPrescaler) refers to stm32f10x_wwdg.o(.text.WWDG_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetWindowValue) refers to stm32f10x_wwdg.o(.text.WWDG_SetWindowValue) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_EnableIT) refers to stm32f10x_wwdg.o(.text.WWDG_EnableIT) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetCounter) refers to stm32f10x_wwdg.o(.text.WWDG_SetCounter) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_Enable) refers to stm32f10x_wwdg.o(.text.WWDG_Enable) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_GetFlagStatus) refers to stm32f10x_wwdg.o(.text.WWDG_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_ClearFlag) refers to stm32f10x_wwdg.o(.text.WWDG_ClearFlag) for [Anonymous Symbol]
    delay.o(.ARM.exidx.text.Delay_us) refers to delay.o(.text.Delay_us) for [Anonymous Symbol]
    delay.o(.ARM.exidx.text.Delay_ms) refers to delay.o(.text.Delay_ms) for [Anonymous Symbol]
    delay.o(.ARM.exidx.text.Delay_s) refers to delay.o(.text.Delay_s) for [Anonymous Symbol]
    sys.o(.text.NVIC_Configuration) refers to misc.o(.text.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    sys.o(.ARM.exidx.text.NVIC_Configuration) refers to sys.o(.text.NVIC_Configuration) for [Anonymous Symbol]
    key.o(.text.Key_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    key.o(.text.Key_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    key.o(.ARM.exidx.text.Key_Init) refers to key.o(.text.Key_Init) for [Anonymous Symbol]
    key.o(.text.Key_GetNum) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(.text.Key_GetNum) refers to delay.o(.text.Delay_ms) for Delay_ms
    key.o(.ARM.exidx.text.Key_GetNum) refers to key.o(.text.Key_GetNum) for [Anonymous Symbol]
    pwm.o(.text.PWM_Init) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    pwm.o(.text.PWM_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pwm.o(.text.PWM_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_TimeBaseInit) for TIM_TimeBaseInit
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC1Init) for TIM_OC1Init
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC2Init) for TIM_OC2Init
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC3Init) for TIM_OC3Init
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC4Init) for TIM_OC4Init
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm.o(.ARM.exidx.text.PWM_Init) refers to pwm.o(.text.PWM_Init) for [Anonymous Symbol]
    irobstacle.o(.text.Irobstacle_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    irobstacle.o(.text.Irobstacle_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    irobstacle.o(.ARM.exidx.text.Irobstacle_Init) refers to irobstacle.o(.text.Irobstacle_Init) for [Anonymous Symbol]
    irobstacle.o(.text.Left_Irobstacle_Get) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    irobstacle.o(.ARM.exidx.text.Left_Irobstacle_Get) refers to irobstacle.o(.text.Left_Irobstacle_Get) for [Anonymous Symbol]
    irobstacle.o(.text.Right_Irobstacle_Get) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    irobstacle.o(.ARM.exidx.text.Right_Irobstacle_Get) refers to irobstacle.o(.text.Right_Irobstacle_Get) for [Anonymous Symbol]
    ledseg.o(.text.LEDSEG_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    ledseg.o(.text.LEDSEG_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    ledseg.o(.text.LEDSEG_Init) refers to stm32f10x_gpio.o(.text.GPIO_SetBits) for GPIO_SetBits
    ledseg.o(.ARM.exidx.text.LEDSEG_Init) refers to ledseg.o(.text.LEDSEG_Init) for [Anonymous Symbol]
    ledseg.o(.text.Digital_Display) refers to ledseg.o(.data.LedShowData) for LedShowData
    ledseg.o(.text.Digital_Display) refers to ledseg.o(.data.GPIO_PIN_x) for GPIO_PIN_x
    ledseg.o(.text.Digital_Display) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    ledseg.o(.ARM.exidx.text.Digital_Display) refers to ledseg.o(.text.Digital_Display) for [Anonymous Symbol]
    incontrol.o(.text.IRremote_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    incontrol.o(.text.IRremote_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    incontrol.o(.text.IRremote_Init) refers to stm32f10x_gpio.o(.text.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    incontrol.o(.text.IRremote_Init) refers to stm32f10x_exti.o(.text.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    incontrol.o(.text.IRremote_Init) refers to stm32f10x_exti.o(.text.EXTI_Init) for EXTI_Init
    incontrol.o(.text.IRremote_Init) refers to misc.o(.text.NVIC_Init) for NVIC_Init
    incontrol.o(.ARM.exidx.text.IRremote_Init) refers to incontrol.o(.text.IRremote_Init) for [Anonymous Symbol]
    incontrol.o(.text.IRremote_Counttime) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    incontrol.o(.text.IRremote_Counttime) refers to delay.o(.text.Delay_us) for Delay_us
    incontrol.o(.ARM.exidx.text.IRremote_Counttime) refers to incontrol.o(.text.IRremote_Counttime) for [Anonymous Symbol]
    incontrol.o(.text.EXTI9_5_IRQHandler) refers to incontrol.o(.bss.IR_Receivecode) for IR_Receivecode
    incontrol.o(.text.EXTI9_5_IRQHandler) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    incontrol.o(.text.EXTI9_5_IRQHandler) refers to delay.o(.text.Delay_us) for Delay_us
    incontrol.o(.text.EXTI9_5_IRQHandler) refers to incontrol.o(.bss.IR_Receiveflag) for IR_Receiveflag
    incontrol.o(.text.EXTI9_5_IRQHandler) refers to stm32f10x_exti.o(.text.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    incontrol.o(.ARM.exidx.text.EXTI9_5_IRQHandler) refers to incontrol.o(.text.EXTI9_5_IRQHandler) for [Anonymous Symbol]
    usart1.o(.text.USART1_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart1.o(.text.USART1_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    usart1.o(.text.USART1_Init) refers to stm32f10x_usart.o(.text.USART_Init) for USART_Init
    usart1.o(.text.USART1_Init) refers to stm32f10x_usart.o(.text.USART_Cmd) for USART_Cmd
    usart1.o(.text.USART1_Init) refers to misc.o(.text.NVIC_Init) for NVIC_Init
    usart1.o(.text.USART1_Init) refers to stm32f10x_usart.o(.text.USART_ITConfig) for USART_ITConfig
    usart1.o(.text.USART1_Init) refers to stm32f10x_usart.o(.text.USART_ClearFlag) for USART_ClearFlag
    usart1.o(.ARM.exidx.text.USART1_Init) refers to usart1.o(.text.USART1_Init) for [Anonymous Symbol]
    usart1.o(.text.USART1_IRQHandler) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for USART_GetFlagStatus
    usart1.o(.text.USART1_IRQHandler) refers to stm32f10x_usart.o(.text.USART_ClearFlag) for USART_ClearFlag
    usart1.o(.text.USART1_IRQHandler) refers to stm32f10x_usart.o(.text.USART_ReceiveData) for USART_ReceiveData
    usart1.o(.text.USART1_IRQHandler) refers to usart1.o(.bss.RxData) for RxData
    usart1.o(.ARM.exidx.text.USART1_IRQHandler) refers to usart1.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    ir_track.o(.text.Track_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    ir_track.o(.text.Track_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    ir_track.o(.ARM.exidx.text.Track_Init) refers to ir_track.o(.text.Track_Init) for [Anonymous Symbol]
    ir_track.o(.text.Track_Get_Left2) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    ir_track.o(.ARM.exidx.text.Track_Get_Left2) refers to ir_track.o(.text.Track_Get_Left2) for [Anonymous Symbol]
    ir_track.o(.text.Track_Get_Left1) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    ir_track.o(.ARM.exidx.text.Track_Get_Left1) refers to ir_track.o(.text.Track_Get_Left1) for [Anonymous Symbol]
    ir_track.o(.text.Track_Get_Center) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    ir_track.o(.ARM.exidx.text.Track_Get_Center) refers to ir_track.o(.text.Track_Get_Center) for [Anonymous Symbol]
    ir_track.o(.text.Track_Get_Right1) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    ir_track.o(.ARM.exidx.text.Track_Get_Right1) refers to ir_track.o(.text.Track_Get_Right1) for [Anonymous Symbol]
    ir_track.o(.text.Track_Get_Right2) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    ir_track.o(.ARM.exidx.text.Track_Get_Right2) refers to ir_track.o(.text.Track_Get_Right2) for [Anonymous Symbol]
    ir_track.o(.text.Track) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    ir_track.o(.text.Track) refers to motor.o(.text.Motor_Run) for Motor_Run
    ir_track.o(.text.Track) refers to motor.o(.text.Set_Motor) for Set_Motor
    ir_track.o(.ARM.exidx.text.Track) refers to ir_track.o(.text.Track) for [Anonymous Symbol]
    oled.o(.text.OLED_I2C_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(.text.OLED_I2C_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    oled.o(.text.OLED_I2C_Init) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_I2C_Init) refers to oled.o(.text.OLED_I2C_Init) for [Anonymous Symbol]
    oled.o(.text.OLED_I2C_Start) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_I2C_Start) refers to oled.o(.text.OLED_I2C_Start) for [Anonymous Symbol]
    oled.o(.text.OLED_I2C_Stop) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_I2C_Stop) refers to oled.o(.text.OLED_I2C_Stop) for [Anonymous Symbol]
    oled.o(.text.OLED_I2C_SendByte) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_I2C_SendByte) refers to oled.o(.text.OLED_I2C_SendByte) for [Anonymous Symbol]
    oled.o(.text.OLED_WriteCommand) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_WriteCommand) refers to oled.o(.text.OLED_WriteCommand) for [Anonymous Symbol]
    oled.o(.text.OLED_WriteData) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_WriteData) refers to oled.o(.text.OLED_WriteData) for [Anonymous Symbol]
    oled.o(.text.OLED_SetCursor) refers to oled.o(.text.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(.ARM.exidx.text.OLED_SetCursor) refers to oled.o(.text.OLED_SetCursor) for [Anonymous Symbol]
    oled.o(.text.OLED_Clear) refers to oled.o(.text.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(.text.OLED_Clear) refers to oled.o(.text.OLED_WriteData) for OLED_WriteData
    oled.o(.ARM.exidx.text.OLED_Clear) refers to oled.o(.text.OLED_Clear) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowChar) refers to oled.o(.text.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowChar) refers to oled.o(.text.OLED_WriteData) for OLED_WriteData
    oled.o(.ARM.exidx.text.OLED_ShowChar) refers to oled.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowString) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_Pow) refers to oled.o(.text.OLED_Pow) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowNum) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowNum) refers to oled.o(.text.OLED_ShowNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowSignedNum) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowSignedNum) refers to oled.o(.text.OLED_ShowSignedNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowHexNum) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowHexNum) refers to oled.o(.text.OLED_ShowHexNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowBinNum) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowBinNum) refers to oled.o(.text.OLED_ShowBinNum) for [Anonymous Symbol]
    oled.o(.text.OLED_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(.text.OLED_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    oled.o(.text.OLED_Init) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_WriteData) for OLED_WriteData
    oled.o(.ARM.exidx.text.OLED_Init) refers to oled.o(.text.OLED_Init) for [Anonymous Symbol]
    motor.o(.text.Motor_Init) refers to pwm.o(.text.PWM_Init) for PWM_Init
    motor.o(.ARM.exidx.text.Motor_Init) refers to motor.o(.text.Motor_Init) for [Anonymous Symbol]
    motor.o(.text.Set_Motor) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for TIM_SetCompare1
    motor.o(.text.Set_Motor) refers to stm32f10x_tim.o(.text.TIM_SetCompare2) for TIM_SetCompare2
    motor.o(.text.Set_Motor) refers to stm32f10x_tim.o(.text.TIM_SetCompare3) for TIM_SetCompare3
    motor.o(.text.Set_Motor) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for TIM_SetCompare4
    motor.o(.ARM.exidx.text.Set_Motor) refers to motor.o(.text.Set_Motor) for [Anonymous Symbol]
    motor.o(.text.Motor_Run) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for TIM_SetCompare1
    motor.o(.text.Motor_Run) refers to stm32f10x_tim.o(.text.TIM_SetCompare2) for TIM_SetCompare2
    motor.o(.text.Motor_Run) refers to stm32f10x_tim.o(.text.TIM_SetCompare3) for TIM_SetCompare3
    motor.o(.text.Motor_Run) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for TIM_SetCompare4
    motor.o(.ARM.exidx.text.Motor_Run) refers to motor.o(.text.Motor_Run) for [Anonymous Symbol]
    motor.o(.text.Motor_Brake) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for TIM_SetCompare1
    motor.o(.text.Motor_Brake) refers to stm32f10x_tim.o(.text.TIM_SetCompare2) for TIM_SetCompare2
    motor.o(.text.Motor_Brake) refers to stm32f10x_tim.o(.text.TIM_SetCompare3) for TIM_SetCompare3
    motor.o(.text.Motor_Brake) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for TIM_SetCompare4
    motor.o(.ARM.exidx.text.Motor_Brake) refers to motor.o(.text.Motor_Brake) for [Anonymous Symbol]
    motor.o(.text.Motor_Back) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for TIM_SetCompare1
    motor.o(.text.Motor_Back) refers to stm32f10x_tim.o(.text.TIM_SetCompare2) for TIM_SetCompare2
    motor.o(.text.Motor_Back) refers to stm32f10x_tim.o(.text.TIM_SetCompare3) for TIM_SetCompare3
    motor.o(.text.Motor_Back) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for TIM_SetCompare4
    motor.o(.ARM.exidx.text.Motor_Back) refers to motor.o(.text.Motor_Back) for [Anonymous Symbol]
    motor.o(.text.Motor_Left) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for TIM_SetCompare1
    motor.o(.text.Motor_Left) refers to stm32f10x_tim.o(.text.TIM_SetCompare2) for TIM_SetCompare2
    motor.o(.text.Motor_Left) refers to stm32f10x_tim.o(.text.TIM_SetCompare3) for TIM_SetCompare3
    motor.o(.text.Motor_Left) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for TIM_SetCompare4
    motor.o(.ARM.exidx.text.Motor_Left) refers to motor.o(.text.Motor_Left) for [Anonymous Symbol]
    motor.o(.text.Motor_Right) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for TIM_SetCompare1
    motor.o(.text.Motor_Right) refers to stm32f10x_tim.o(.text.TIM_SetCompare2) for TIM_SetCompare2
    motor.o(.text.Motor_Right) refers to stm32f10x_tim.o(.text.TIM_SetCompare3) for TIM_SetCompare3
    motor.o(.text.Motor_Right) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for TIM_SetCompare4
    motor.o(.ARM.exidx.text.Motor_Right) refers to motor.o(.text.Motor_Right) for [Anonymous Symbol]
    motor.o(.text.Motor_SpinLeft) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for TIM_SetCompare1
    motor.o(.text.Motor_SpinLeft) refers to stm32f10x_tim.o(.text.TIM_SetCompare2) for TIM_SetCompare2
    motor.o(.text.Motor_SpinLeft) refers to stm32f10x_tim.o(.text.TIM_SetCompare3) for TIM_SetCompare3
    motor.o(.text.Motor_SpinLeft) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for TIM_SetCompare4
    motor.o(.ARM.exidx.text.Motor_SpinLeft) refers to motor.o(.text.Motor_SpinLeft) for [Anonymous Symbol]
    motor.o(.text.Motor_SpinRight) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for TIM_SetCompare1
    motor.o(.text.Motor_SpinRight) refers to stm32f10x_tim.o(.text.TIM_SetCompare2) for TIM_SetCompare2
    motor.o(.text.Motor_SpinRight) refers to stm32f10x_tim.o(.text.TIM_SetCompare3) for TIM_SetCompare3
    motor.o(.text.Motor_SpinRight) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for TIM_SetCompare4
    motor.o(.ARM.exidx.text.Motor_SpinRight) refers to motor.o(.text.Motor_SpinRight) for [Anonymous Symbol]
    main.o(.text.main) refers to key.o(.text.Key_Init) for Key_Init
    main.o(.text.main) refers to oled.o(.text.OLED_Init) for OLED_Init
    main.o(.text.main) refers to ir_track.o(.text.Track_Init) for Track_Init
    main.o(.text.main) refers to misc.o(.text.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    main.o(.text.main) refers to incontrol.o(.text.IRremote_Init) for IRremote_Init
    main.o(.text.main) refers to motor.o(.text.Motor_Init) for Motor_Init
    main.o(.text.main) refers to usart1.o(.text.USART1_Init) for USART1_Init
    main.o(.text.main) refers to main.o(.bss.Key) for Key
    main.o(.text.main) refers to main.o(.bss.Car_Mode) for Car_Mode
    main.o(.text.main) refers to usart1.o(.bss.RxData) for RxData
    main.o(.text.main) refers to ir_track.o(.text.Track) for Track
    main.o(.text.main) refers to key.o(.text.Key_GetNum) for Key_GetNum
    main.o(.text.main) refers to oled.o(.text.OLED_ShowString) for OLED_ShowString
    main.o(.text.main) refers to motor.o(.text.Motor_Brake) for Motor_Brake
    main.o(.text.main) refers to motor.o(.text.Motor_SpinRight) for Motor_SpinRight
    main.o(.text.main) refers to motor.o(.text.Motor_Back) for Motor_Back
    main.o(.text.main) refers to motor.o(.text.Motor_SpinLeft) for Motor_SpinLeft
    main.o(.text.main) refers to motor.o(.text.Motor_Run) for Motor_Run
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f10x_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f10x_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f10x_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f10x_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f10x_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f10x_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f10x_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f10x_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f10x_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f10x_md.o(HEAP), (512 bytes).
    Removing core_cm3.o(.text), (0 bytes).
    Removing core_cm3.o(.text.__get_PSP), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_PSP), (8 bytes).
    Removing core_cm3.o(.text.__set_PSP), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_PSP), (8 bytes).
    Removing core_cm3.o(.text.__get_MSP), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_MSP), (8 bytes).
    Removing core_cm3.o(.text.__set_MSP), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_MSP), (8 bytes).
    Removing core_cm3.o(.text.__get_BASEPRI), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_BASEPRI), (8 bytes).
    Removing core_cm3.o(.text.__set_BASEPRI), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_BASEPRI), (8 bytes).
    Removing core_cm3.o(.text.__get_PRIMASK), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_PRIMASK), (8 bytes).
    Removing core_cm3.o(.text.__set_PRIMASK), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_PRIMASK), (8 bytes).
    Removing core_cm3.o(.text.__get_FAULTMASK), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_FAULTMASK), (8 bytes).
    Removing core_cm3.o(.text.__set_FAULTMASK), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_FAULTMASK), (8 bytes).
    Removing core_cm3.o(.text.__get_CONTROL), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_CONTROL), (8 bytes).
    Removing core_cm3.o(.text.__set_CONTROL), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_CONTROL), (8 bytes).
    Removing core_cm3.o(.text.__REV), (4 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__REV), (8 bytes).
    Removing core_cm3.o(.text.__REV16), (4 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__REV16), (8 bytes).
    Removing core_cm3.o(.text.__REVSH), (4 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__REVSH), (8 bytes).
    Removing core_cm3.o(.text.__RBIT), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__RBIT), (8 bytes).
    Removing core_cm3.o(.text.__LDREXB), (8 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__LDREXB), (8 bytes).
    Removing core_cm3.o(.text.__LDREXH), (8 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__LDREXH), (8 bytes).
    Removing core_cm3.o(.text.__LDREXW), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__LDREXW), (8 bytes).
    Removing core_cm3.o(.text.__STREXB), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__STREXB), (8 bytes).
    Removing core_cm3.o(.text.__STREXH), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__STREXH), (8 bytes).
    Removing core_cm3.o(.text.__STREXW), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__STREXW), (8 bytes).
    Removing system_stm32f10x.o(.text), (0 bytes).
    Removing system_stm32f10x.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f10x.o(.text.SystemCoreClockUpdate), (94 bytes).
    Removing system_stm32f10x.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing system_stm32f10x.o(.data.SystemCoreClock), (4 bytes).
    Removing system_stm32f10x.o(.rodata.AHBPrescTable), (16 bytes).
    Removing misc.o(.text), (0 bytes).
    Removing misc.o(.ARM.exidx.text.NVIC_PriorityGroupConfig), (8 bytes).
    Removing misc.o(.ARM.exidx.text.NVIC_Init), (8 bytes).
    Removing misc.o(.text.NVIC_SetVectorTable), (24 bytes).
    Removing misc.o(.ARM.exidx.text.NVIC_SetVectorTable), (8 bytes).
    Removing misc.o(.text.NVIC_SystemLPConfig), (24 bytes).
    Removing misc.o(.ARM.exidx.text.NVIC_SystemLPConfig), (8 bytes).
    Removing misc.o(.text.SysTick_CLKSourceConfig), (24 bytes).
    Removing misc.o(.ARM.exidx.text.SysTick_CLKSourceConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text), (0 bytes).
    Removing stm32f10x_adc.o(.text.ADC_DeInit), (76 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_DeInit), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_Init), (82 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_Init), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_StructInit), (16 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_StructInit), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_Cmd), (16 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_Cmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_DMACmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_DMACmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ITConfig), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ITConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ResetCalibration), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetResetCalibrationStatus), (8 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetResetCalibrationStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_StartCalibration), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetCalibrationStatus), (8 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetCalibrationStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_SoftwareStartConvCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_SoftwareStartConvCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetSoftwareStartConvStatus), (8 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetSoftwareStartConvStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_DiscModeChannelCountConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_DiscModeCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_DiscModeCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_RegularChannelConfig), (96 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_RegularChannelConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ExternalTrigConvCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigConvCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetConversionValue), (6 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetDualModeConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_AutoInjectedConvCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_AutoInjectedConvCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_InjectedDiscModeCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedDiscModeCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ExternalTrigInjectedConvConfig), (12 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigInjectedConvConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ExternalTrigInjectedConvCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigInjectedConvCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_SoftwareStartInjectedConvCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_SoftwareStartInjectedConvCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetSoftwareStartInjectedConvCmdStatus), (8 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetSoftwareStartInjectedConvCmdStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_InjectedChannelConfig), (78 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedChannelConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_InjectedSequencerLengthConfig), (22 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedSequencerLengthConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_SetInjectedOffset), (22 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_SetInjectedOffset), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetInjectedConversionValue), (26 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetInjectedConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_AnalogWatchdogCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogThresholdsConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_AnalogWatchdogSingleChannelConfig), (12 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogSingleChannelConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_TempSensorVrefintCmd), (26 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_TempSensorVrefintCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetFlagStatus), (10 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetFlagStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ClearFlag), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetITStatus), (26 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetITStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_bkp.o(.text), (0 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_DeInit), (18 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_DeInit), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_TamperPinLevelConfig), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_TamperPinCmd), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_ITConfig), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_RTCOutputConfig), (20 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_RTCOutputConfig), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_SetRTCCalibrationValue), (20 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_SetRTCCalibrationValue), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_WriteBackupRegister), (30 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_WriteBackupRegister), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_ReadBackupRegister), (30 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_ReadBackupRegister), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_GetFlagStatus), (14 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_GetFlagStatus), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_ClearFlag), (18 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_ClearFlag), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_GetITStatus), (14 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_GetITStatus), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_ClearITPendingBit), (18 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_can.o(.text), (0 bytes).
    Removing stm32f10x_can.o(.text.CAN_DeInit), (42 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_DeInit), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_Init), (228 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_Init), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_FilterInit), (172 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_FilterInit), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_StructInit), (20 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_StructInit), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_SlaveStartBank), (44 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_SlaveStartBank), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_DBGFreeze), (18 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_DBGFreeze), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_TTComModeCmd), (92 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_TTComModeCmd), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_Transmit), (160 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_Transmit), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_TransmitStatus), (128 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_TransmitStatus), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_CancelTransmit), (32 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_CancelTransmit), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_Receive), (160 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_Receive), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_FIFORelease), (18 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_FIFORelease), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_MessagePending), (24 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_MessagePending), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_OperatingModeRequest), (146 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_OperatingModeRequest), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_Sleep), (26 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_Sleep), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_WakeUp), (44 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_WakeUp), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_GetLastErrorCode), (8 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_GetLastErrorCode), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_GetReceiveErrorCounter), (6 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_GetReceiveErrorCounter), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_GetLSBTransmitErrorCounter), (8 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_GetLSBTransmitErrorCounter), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_ITConfig), (16 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_ITConfig), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_GetFlagStatus), (52 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_GetFlagStatus), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_ClearFlag), (58 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_ClearFlag), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_GetITStatus), (280 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_GetITStatus), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_ClearITPendingBit), (172 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_cec.o(.text), (0 bytes).
    Removing stm32f10x_cec.o(.text.CEC_DeInit), (26 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_DeInit), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_Init), (28 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_Init), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_Cmd), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_ITConfig), (10 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_ITConfig), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_OwnAddressConfig), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_SetPrescaler), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_SendDataByte), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_ReceiveDataByte), (14 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_ReceiveDataByte), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_StartOfMessage), (14 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_StartOfMessage), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_EndOfMessageCmd), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_GetFlagStatus), (34 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_GetFlagStatus), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_ClearFlag), (34 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_ClearFlag), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_GetITStatus), (28 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_GetITStatus), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_ClearITPendingBit), (34 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_crc.o(.text), (0 bytes).
    Removing stm32f10x_crc.o(.text.CRC_ResetDR), (14 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_ResetDR), (8 bytes).
    Removing stm32f10x_crc.o(.text.CRC_CalcCRC), (14 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_CalcCRC), (8 bytes).
    Removing stm32f10x_crc.o(.text.CRC_CalcBlockCRC), (26 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_CalcBlockCRC), (8 bytes).
    Removing stm32f10x_crc.o(.text.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_GetCRC), (8 bytes).
    Removing stm32f10x_crc.o(.text.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_SetIDRegister), (8 bytes).
    Removing stm32f10x_crc.o(.text.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_GetIDRegister), (8 bytes).
    Removing stm32f10x_dac.o(.text), (0 bytes).
    Removing stm32f10x_dac.o(.text.DAC_DeInit), (26 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_DeInit), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_Init), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_StructInit), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_Cmd), (30 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_Cmd), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_DMACmd), (32 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_DMACmd), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_SoftwareTriggerCmd), (32 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_SoftwareTriggerCmd), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_DualSoftwareTriggerCmd), (24 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_DualSoftwareTriggerCmd), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_WaveGenerationCmd), (28 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_WaveGenerationCmd), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_SetChannel1Data), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_SetChannel2Data), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_SetDualChannelData), (24 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_SetDualChannelData), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_GetDataOutputValue), (8 bytes).
    Removing stm32f10x_dbgmcu.o(.text), (0 bytes).
    Removing stm32f10x_dbgmcu.o(.text.DBGMCU_GetREVID), (14 bytes).
    Removing stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_GetREVID), (8 bytes).
    Removing stm32f10x_dbgmcu.o(.text.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_GetDEVID), (8 bytes).
    Removing stm32f10x_dbgmcu.o(.text.DBGMCU_Config), (24 bytes).
    Removing stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_Config), (8 bytes).
    Removing stm32f10x_dma.o(.text), (0 bytes).
    Removing stm32f10x_dma.o(.text.DMA_DeInit), (268 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_DeInit), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_Init), (72 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_Init), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_StructInit), (10 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_StructInit), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_Cmd), (22 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_Cmd), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_ITConfig), (16 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_ITConfig), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_SetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_GetCurrDataCounter), (6 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_GetFlagStatus), (22 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_GetFlagStatus), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_ClearFlag), (16 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_ClearFlag), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_GetITStatus), (22 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_GetITStatus), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_exti.o(.text), (0 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_DeInit), (30 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_DeInit), (8 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_Init), (8 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_StructInit), (14 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_StructInit), (8 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_GenerateSWInterrupt), (8 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_GetFlagStatus), (18 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_GetFlagStatus), (8 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_ClearFlag), (8 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_GetITStatus), (30 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_GetITStatus), (8 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_flash.o(.text), (0 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_SetLatency), (20 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_SetLatency), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_HalfCycleAccessCmd), (24 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_HalfCycleAccessCmd), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_PrefetchBufferCmd), (24 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_PrefetchBufferCmd), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_Unlock), (30 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_Unlock), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_UnlockBank1), (30 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_UnlockBank1), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_Lock), (18 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_Lock), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_LockBank1), (18 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_LockBank1), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ErasePage), (254 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ErasePage), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_WaitForLastOperation), (118 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_EraseAllPages), (246 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseAllPages), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_EraseAllBank1Pages), (246 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseAllBank1Pages), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_WaitForLastBank1Operation), (118 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_WaitForLastBank1Operation), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_EraseOptionBytes), (436 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseOptionBytes), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetReadOutProtectionStatus), (16 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetReadOutProtectionStatus), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ProgramWord), (492 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramWord), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ProgramHalfWord), (278 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramHalfWord), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ProgramOptionByteData), (312 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramOptionByteData), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_EnableWriteProtection), (664 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_EnableWriteProtection), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ReadOutProtection), (440 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ReadOutProtection), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_UserOptionByteConfig), (328 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_UserOptionByteConfig), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetUserOptionByte), (14 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetUserOptionByte), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetWriteProtectionOptionByte), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetPrefetchBufferStatus), (16 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetPrefetchBufferStatus), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ITConfig), (24 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ITConfig), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetFlagStatus), (30 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetFlagStatus), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ClearFlag), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetStatus), (42 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetStatus), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetBank1Status), (42 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetBank1Status), (8 bytes).
    Removing stm32f10x_fsmc.o(.text), (0 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NORSRAMDeInit), (50 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMDeInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NANDDeInit), (56 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDDeInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_PCCARDDeInit), (26 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDDeInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NORSRAMInit), (192 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NANDInit), (122 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_PCCARDInit), (110 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NORSRAMStructInit), (80 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMStructInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NANDStructInit), (42 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDStructInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_PCCARDStructInit), (42 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDStructInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NORSRAMCmd), (36 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMCmd), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NANDCmd), (64 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDCmd), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_PCCARDCmd), (32 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDCmd), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NANDECCCmd), (64 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDECCCmd), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_GetECC), (22 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetECC), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_ITConfig), (84 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ITConfig), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_GetFlagStatus), (30 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetFlagStatus), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_ClearFlag), (28 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ClearFlag), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_GetITStatus), (44 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetITStatus), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_gpio.o(.text), (0 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_DeInit), (52 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_DeInit), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_AFIODeInit), (22 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_AFIODeInit), (8 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_Init), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_StructInit), (12 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_StructInit), (8 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadInputDataBit), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_ReadInputData), (6 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_ReadOutputDataBit), (10 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadOutputDataBit), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_SetBits), (4 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_SetBits), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_ResetBits), (4 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ResetBits), (8 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_WriteBit), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_Write), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_PinLockConfig), (16 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_PinLockConfig), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_EventOutputConfig), (30 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EventOutputConfig), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_EventOutputCmd), (10 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EventOutputCmd), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_PinRemapConfig), (128 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_PinRemapConfig), (8 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EXTILineConfig), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_ETH_MediaInterfaceConfig), (10 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ETH_MediaInterfaceConfig), (8 bytes).
    Removing stm32f10x_gpio.o(.rodata..Lswitch.table.GPIO_DeInit.1), (28 bytes).
    Removing stm32f10x_i2c.o(.text), (0 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_DeInit), (42 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_DeInit), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_Init), (202 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_Init), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_StructInit), (24 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_StructInit), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_Cmd), (18 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_Cmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_DMACmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_DMACmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_DMALastTransferCmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_DMALastTransferCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GenerateSTART), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GenerateSTART), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GenerateSTOP), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GenerateSTOP), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_AcknowledgeConfig), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_AcknowledgeConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_OwnAddress2Config), (12 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_OwnAddress2Config), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_DualAddressCmd), (18 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_DualAddressCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GeneralCallCmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GeneralCallCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ITConfig), (16 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ITConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_SendData), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ReceiveData), (6 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_Send7bitAddress), (16 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_Send7bitAddress), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ReadRegister), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_SoftwareResetCmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_SoftwareResetCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_NACKPositionConfig), (24 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_NACKPositionConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_SMBusAlertConfig), (24 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_SMBusAlertConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_TransmitPEC), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_TransmitPEC), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_PECPositionConfig), (24 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_PECPositionConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_CalculatePEC), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_CalculatePEC), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GetPEC), (6 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ARPCmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ARPCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_StretchClockCmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_StretchClockCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_FastModeDutyCycleConfig), (24 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_FastModeDutyCycleConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_CheckEvent), (22 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_CheckEvent), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GetLastEvent), (12 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetLastEvent), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GetFlagStatus), (48 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetFlagStatus), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ClearFlag), (6 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ClearFlag), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GetITStatus), (28 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetITStatus), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_iwdg.o(.text), (0 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_WriteAccessCmd), (8 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_SetPrescaler), (8 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_SetReload), (8 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_ReloadCounter), (8 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_Enable), (8 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_GetFlagStatus), (18 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_GetFlagStatus), (8 bytes).
    Removing stm32f10x_pwr.o(.text), (0 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_DeInit), (26 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_DeInit), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_BackupAccessCmd), (10 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_BackupAccessCmd), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_PVDCmd), (10 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_PVDCmd), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_PVDLevelConfig), (20 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_PVDLevelConfig), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_WakeUpPinCmd), (10 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_WakeUpPinCmd), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_EnterSTOPMode), (58 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_EnterSTANDBYMode), (44 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_GetFlagStatus), (18 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_GetFlagStatus), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_ClearFlag), (18 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_ClearFlag), (8 bytes).
    Removing stm32f10x_rcc.o(.text), (0 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_DeInit), (66 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_DeInit), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_HSEConfig), (50 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_HSEConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_WaitForHSEStartUp), (48 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_WaitForHSEStartUp), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_GetFlagStatus), (38 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetFlagStatus), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_AdjustHSICalibrationValue), (22 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_AdjustHSICalibrationValue), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_HSICmd), (10 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_HSICmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_PLLConfig), (22 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_PLLConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_PLLCmd), (10 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_PLLCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_SYSCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_SYSCLKConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetSYSCLKSource), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_HCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_HCLKConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_PCLK1Config), (20 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_PCLK1Config), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_PCLK2Config), (22 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_PCLK2Config), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_ITConfig), (24 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_ITConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_USBCLKConfig), (10 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_USBCLKConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_ADCCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_ADCCLKConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_LSEConfig), (30 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_LSEConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_LSICmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_RTCCLKConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_RTCCLKCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetClocksFreq), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_AHBPeriphClockCmd), (24 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_AHBPeriphClockCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB2PeriphClockCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB1PeriphClockCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd), (24 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB2PeriphResetCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd), (24 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB1PeriphResetCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_BackupResetCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_ClockSecuritySystemCmd), (10 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClockSecuritySystemCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_MCOConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_ClearFlag), (18 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClearFlag), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_GetITStatus), (18 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetITStatus), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_rtc.o(.text), (0 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_ITConfig), (24 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_ITConfig), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_EnterConfigMode), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_EnterConfigMode), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_ExitConfigMode), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_ExitConfigMode), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_GetCounter), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetCounter), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_SetCounter), (32 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetCounter), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_SetPrescaler), (34 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetPrescaler), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_SetAlarm), (32 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetAlarm), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_GetDivider), (22 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetDivider), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_WaitForLastTask), (16 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_WaitForLastTask), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_WaitForSynchro), (24 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_WaitForSynchro), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetFlagStatus), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_ClearFlag), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_ClearFlag), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_GetITStatus), (34 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetITStatus), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_ClearITPendingBit), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_sdio.o(.text), (0 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_DeInit), (38 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DeInit), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_Init), (52 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_Init), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StructInit), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_ClockCmd), (10 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClockCmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SetPowerState), (24 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetPowerState), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetPowerState), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_ITConfig), (24 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ITConfig), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DMACmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SendCommand), (46 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendCommand), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CmdStructInit), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetCommandResponse), (14 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetCommandResponse), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetResponse), (26 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetResponse), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_DataConfig), (52 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DataConfig), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DataStructInit), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetDataCounter), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ReadData), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_WriteData), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetFIFOCount), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StartSDIOReadWait), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StopSDIOReadWait), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetSDIOReadWaitMode), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetSDIOOperation), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendSDIOSuspendCmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CommandCompletionCmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_CEATAITCmd), (18 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CEATAITCmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendCEATACmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetFlagStatus), (18 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetFlagStatus), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClearFlag), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetITStatus), (18 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetITStatus), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_spi.o(.text), (0 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_DeInit), (104 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_DeInit), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_Init), (70 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_Init), (8 bytes).
    Removing stm32f10x_spi.o(.text.I2S_Init), (162 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.I2S_Init), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_StructInit), (16 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_StructInit), (8 bytes).
    Removing stm32f10x_spi.o(.text.I2S_StructInit), (14 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.I2S_StructInit), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_Cmd), (20 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_Cmd), (8 bytes).
    Removing stm32f10x_spi.o(.text.I2S_Cmd), (20 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.I2S_Cmd), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_ITConfig), (24 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ITConfig), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_DMACmd), (16 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_DMACmd), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_SendData), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_ReceiveData), (4 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ReceiveData), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_NSSInternalSoftwareConfig), (24 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_NSSInternalSoftwareConfig), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_SSOutputCmd), (20 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_SSOutputCmd), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_DataSizeConfig), (16 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_DataSizeConfig), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_TransmitCRC), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_CalculateCRC), (20 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_CalculateCRC), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_GetCRC), (12 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_GetCRC), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_GetCRCPolynomial), (4 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_GetCRCPolynomial), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_BiDirectionalLineConfig), (24 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_BiDirectionalLineConfig), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_GetFlagStatus), (10 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_GetFlagStatus), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ClearFlag), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_GetITStatus), (24 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_GetITStatus), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_tim.o(.text), (0 bytes).
    Removing stm32f10x_tim.o(.text.TIM_DeInit), (560 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_DeInit), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_TimeBaseInit), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1Init), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2Init), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3Init), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4Init), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ICInit), (608 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ICInit), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetIC1Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC1Prescaler), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetIC2Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC2Prescaler), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetIC3Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC3Prescaler), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetIC4Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC4Prescaler), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_PWMIConfig), (596 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_PWMIConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_BDTRConfig), (40 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_BDTRConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_TimeBaseStructInit), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_TimeBaseStructInit), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OCStructInit), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OCStructInit), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ICStructInit), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ICStructInit), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_BDTRStructInit), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_BDTRStructInit), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_Cmd), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs), (24 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_CtrlPWMOutputs), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ITConfig), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ITConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GenerateEvent), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_DMAConfig), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_DMAConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_DMACmd), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_DMACmd), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_InternalClockConfig), (10 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_InternalClockConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ITRxExternalClockConfig), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ITRxExternalClockConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectInputTrigger), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectInputTrigger), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_TIxExternalClockConfig), (280 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_TIxExternalClockConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ETRClockMode1Config), (30 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRClockMode1Config), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ETRConfig), (22 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ETRClockMode2Config), (30 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRClockMode2Config), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_PrescalerConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_CounterModeConfig), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_CounterModeConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_EncoderInterfaceConfig), (48 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_EncoderInterfaceConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ForcedOC1Config), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC1Config), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ForcedOC2Config), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC2Config), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ForcedOC3Config), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC3Config), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ForcedOC4Config), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC4Config), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ARRPreloadConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectCOM), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectCOM), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectCCDMA), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectCCDMA), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_CCPreloadControl), (18 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_CCPreloadControl), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1PreloadConfig), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2PreloadConfig), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3PreloadConfig), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4PreloadConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC1FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1FastConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC2FastConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2FastConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC3FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3FastConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC4FastConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4FastConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ClearOC1Ref), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC1Ref), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ClearOC2Ref), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC2Ref), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ClearOC3Ref), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC3Ref), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ClearOC4Ref), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC4Ref), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC1PolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1PolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC1NPolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1NPolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC2PolarityConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2PolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC2NPolarityConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2NPolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC3PolarityConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3PolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC3NPolarityConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3NPolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC4PolarityConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4PolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_CCxCmd), (32 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_CCxCmd), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_CCxNCmd), (32 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_CCxNCmd), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectOCxM), (84 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOCxM), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_UpdateDisableConfig), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_UpdateDisableConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_UpdateRequestConfig), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_UpdateRequestConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectHallSensor), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectHallSensor), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectOnePulseMode), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOnePulseMode), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectOutputTrigger), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOutputTrigger), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectSlaveMode), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectMasterSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectMasterSlaveMode), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCounter), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetAutoreload), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare1), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare2), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare3), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare4), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetClockDivision), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetClockDivision), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetCapture1), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture1), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetCapture2), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture2), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetCapture3), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture3), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetCapture4), (6 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetCounter), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCounter), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetPrescaler), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetPrescaler), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetFlagStatus), (10 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetFlagStatus), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearFlag), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetITStatus), (24 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetITStatus), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_usart.o(.text), (0 bytes).
    Removing stm32f10x_usart.o(.text.USART_DeInit), (174 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_DeInit), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_Init), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_StructInit), (18 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_StructInit), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ClockInit), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_ClockStructInit), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ClockStructInit), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_Cmd), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ITConfig), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_DMACmd), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_DMACmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SetAddress), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SetAddress), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_WakeUpConfig), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_WakeUpConfig), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_ReceiverWakeUpCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ReceiverWakeUpCmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_LINBreakDetectLengthConfig), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_LINCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_LINCmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ReceiveData), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SendBreak), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SetGuardTime), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SetPrescaler), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SmartCardCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SmartCardCmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SmartCardNACKCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SmartCardNACKCmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_HalfDuplexCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_HalfDuplexCmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_OverSampling8Cmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_OverSampling8Cmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_OneBitMethodCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_OneBitMethodCmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_IrDAConfig), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_IrDAConfig), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_IrDACmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_IrDACmd), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_GetFlagStatus), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ClearFlag), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_GetITStatus), (42 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_GetITStatus), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_ClearITPendingBit), (14 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_wwdg.o(.text), (0 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_DeInit), (26 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_DeInit), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_SetPrescaler), (20 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetPrescaler), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_SetWindowValue), (40 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetWindowValue), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_EnableIT), (14 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_EnableIT), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetCounter), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_Enable), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_GetFlagStatus), (14 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_GetFlagStatus), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_ClearFlag), (14 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_ClearFlag), (8 bytes).
    Removing delay.o(.text), (0 bytes).
    Removing delay.o(.ARM.exidx.text.Delay_us), (8 bytes).
    Removing delay.o(.ARM.exidx.text.Delay_ms), (8 bytes).
    Removing delay.o(.text.Delay_s), (70 bytes).
    Removing delay.o(.ARM.exidx.text.Delay_s), (8 bytes).
    Removing sys.o(.text), (0 bytes).
    Removing sys.o(.text.NVIC_Configuration), (8 bytes).
    Removing sys.o(.ARM.exidx.text.NVIC_Configuration), (8 bytes).
    Removing key.o(.text), (0 bytes).
    Removing key.o(.ARM.exidx.text.Key_Init), (8 bytes).
    Removing key.o(.ARM.exidx.text.Key_GetNum), (8 bytes).
    Removing pwm.o(.text), (0 bytes).
    Removing pwm.o(.ARM.exidx.text.PWM_Init), (8 bytes).
    Removing irobstacle.o(.text), (0 bytes).
    Removing irobstacle.o(.text.Irobstacle_Init), (40 bytes).
    Removing irobstacle.o(.ARM.exidx.text.Irobstacle_Init), (8 bytes).
    Removing irobstacle.o(.text.Left_Irobstacle_Get), (16 bytes).
    Removing irobstacle.o(.ARM.exidx.text.Left_Irobstacle_Get), (8 bytes).
    Removing irobstacle.o(.text.Right_Irobstacle_Get), (16 bytes).
    Removing irobstacle.o(.ARM.exidx.text.Right_Irobstacle_Get), (8 bytes).
    Removing ledseg.o(.text), (0 bytes).
    Removing ledseg.o(.text.LEDSEG_Init), (48 bytes).
    Removing ledseg.o(.ARM.exidx.text.LEDSEG_Init), (8 bytes).
    Removing ledseg.o(.text.Digital_Display), (60 bytes).
    Removing ledseg.o(.ARM.exidx.text.Digital_Display), (8 bytes).
    Removing ledseg.o(.data.LedShowData), (10 bytes).
    Removing ledseg.o(.data.GPIO_PIN_x), (16 bytes).
    Removing incontrol.o(.text), (0 bytes).
    Removing incontrol.o(.ARM.exidx.text.IRremote_Init), (8 bytes).
    Removing incontrol.o(.text.IRremote_Counttime), (46 bytes).
    Removing incontrol.o(.ARM.exidx.text.IRremote_Counttime), (8 bytes).
    Removing incontrol.o(.ARM.exidx.text.EXTI9_5_IRQHandler), (8 bytes).
    Removing usart1.o(.text), (0 bytes).
    Removing usart1.o(.ARM.exidx.text.USART1_Init), (8 bytes).
    Removing usart1.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing ir_track.o(.text), (0 bytes).
    Removing ir_track.o(.ARM.exidx.text.Track_Init), (8 bytes).
    Removing ir_track.o(.text.Track_Get_Left2), (14 bytes).
    Removing ir_track.o(.ARM.exidx.text.Track_Get_Left2), (8 bytes).
    Removing ir_track.o(.text.Track_Get_Left1), (14 bytes).
    Removing ir_track.o(.ARM.exidx.text.Track_Get_Left1), (8 bytes).
    Removing ir_track.o(.text.Track_Get_Center), (14 bytes).
    Removing ir_track.o(.ARM.exidx.text.Track_Get_Center), (8 bytes).
    Removing ir_track.o(.text.Track_Get_Right1), (14 bytes).
    Removing ir_track.o(.ARM.exidx.text.Track_Get_Right1), (8 bytes).
    Removing ir_track.o(.text.Track_Get_Right2), (14 bytes).
    Removing ir_track.o(.ARM.exidx.text.Track_Get_Right2), (8 bytes).
    Removing ir_track.o(.ARM.exidx.text.Track), (8 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.text.OLED_I2C_Init), (84 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_I2C_Init), (8 bytes).
    Removing oled.o(.text.OLED_I2C_Start), (62 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_I2C_Start), (8 bytes).
    Removing oled.o(.text.OLED_I2C_Stop), (50 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_I2C_Stop), (8 bytes).
    Removing oled.o(.text.OLED_I2C_SendByte), (98 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_I2C_SendByte), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_WriteCommand), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_WriteData), (8 bytes).
    Removing oled.o(.text.OLED_SetCursor), (32 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_SetCursor), (8 bytes).
    Removing oled.o(.text.OLED_Clear), (56 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Clear), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowChar), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowString), (8 bytes).
    Removing oled.o(.text.OLED_Pow), (22 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Pow), (8 bytes).
    Removing oled.o(.text.OLED_ShowNum), (114 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowNum), (8 bytes).
    Removing oled.o(.text.OLED_ShowSignedNum), (136 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowSignedNum), (8 bytes).
    Removing oled.o(.text.OLED_ShowHexNum), (104 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowHexNum), (8 bytes).
    Removing oled.o(.text.OLED_ShowBinNum), (90 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowBinNum), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing motor.o(.text), (0 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_Init), (8 bytes).
    Removing motor.o(.ARM.exidx.text.Set_Motor), (8 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_Run), (8 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_Brake), (8 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_Back), (8 bytes).
    Removing motor.o(.text.Motor_Left), (76 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_Left), (8 bytes).
    Removing motor.o(.text.Motor_Right), (78 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_Right), (8 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_SpinLeft), (8 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_SpinRight), (8 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing stm32f10x_it.o(.text), (0 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).

1079 unused section(s) (total 24008 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    Delay.c                                  0x00000000   Number         0  delay.o ABSOLUTE
    Incontrol.c                              0x00000000   Number         0  incontrol.o ABSOLUTE
    Ir_Track.c                               0x00000000   Number         0  ir_track.o ABSOLUTE
    Irobstacle.c                             0x00000000   Number         0  irobstacle.o ABSOLUTE
    Key.c                                    0x00000000   Number         0  key.o ABSOLUTE
    LEDSEG.c                                 0x00000000   Number         0  ledseg.o ABSOLUTE
    Motor.c                                  0x00000000   Number         0  motor.o ABSOLUTE
    OLED.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    PWM.c                                    0x00000000   Number         0  pwm.o ABSOLUTE
    Start\startup_stm32f10x_md.s             0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    USART1.c                                 0x00000000   Number         0  usart1.o ABSOLUTE
    core_cm3.c                               0x00000000   Number         0  core_cm3.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    misc.c                                   0x00000000   Number         0  misc.o ABSOLUTE
    stm32f10x_adc.c                          0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    stm32f10x_bkp.c                          0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    stm32f10x_can.c                          0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    stm32f10x_cec.c                          0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    stm32f10x_crc.c                          0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    stm32f10x_dac.c                          0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    stm32f10x_dbgmcu.c                       0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    stm32f10x_dma.c                          0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    stm32f10x_exti.c                         0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    stm32f10x_flash.c                        0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    stm32f10x_fsmc.c                         0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    stm32f10x_gpio.c                         0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    stm32f10x_i2c.c                          0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    stm32f10x_iwdg.c                         0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    stm32f10x_pwr.c                          0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    stm32f10x_rcc.c                          0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    stm32f10x_rtc.c                          0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    stm32f10x_sdio.c                         0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    stm32f10x_spi.c                          0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    stm32f10x_tim.c                          0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    stm32f10x_usart.c                        0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    stm32f10x_wwdg.c                         0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    sys.c                                    0x00000000   Number         0  sys.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    .ARM.Collect$$$$00000000                 0x080000ec   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080000ec   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080000f0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080000f4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080000f4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080000f4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x080000fc   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x080000fc   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080000fc   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080000fc   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000100   Section       36  startup_stm32f10x_md.o(.text)
    .text                                    0x08000124   Section       48  init.o(.text)
    [Anonymous Symbol]                       0x08000154   Section        0  stm32f10x_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x08000158   Section        0  stm32f10x_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x0800015c   Section        0  delay.o(.text.Delay_ms)
    [Anonymous Symbol]                       0x0800019c   Section        0  delay.o(.text.Delay_us)
    [Anonymous Symbol]                       0x080001c0   Section        0  incontrol.o(.text.EXTI9_5_IRQHandler)
    [Anonymous Symbol]                       0x08000278   Section        0  stm32f10x_exti.o(.text.EXTI_ClearITPendingBit)
    [Anonymous Symbol]                       0x08000284   Section        0  stm32f10x_exti.o(.text.EXTI_Init)
    [Anonymous Symbol]                       0x080002ec   Section        0  stm32f10x_gpio.o(.text.GPIO_EXTILineConfig)
    [Anonymous Symbol]                       0x08000324   Section        0  stm32f10x_gpio.o(.text.GPIO_Init)
    [Anonymous Symbol]                       0x080003e0   Section        0  stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit)
    [Anonymous Symbol]                       0x080003ec   Section        0  stm32f10x_gpio.o(.text.GPIO_WriteBit)
    [Anonymous Symbol]                       0x080003f8   Section        0  stm32f10x_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x080003fc   Section        0  incontrol.o(.text.IRremote_Init)
    [Anonymous Symbol]                       0x0800045c   Section        0  key.o(.text.Key_GetNum)
    [Anonymous Symbol]                       0x08000498   Section        0  key.o(.text.Key_Init)
    [Anonymous Symbol]                       0x080004c0   Section        0  stm32f10x_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x080004c4   Section        0  motor.o(.text.Motor_Back)
    [Anonymous Symbol]                       0x08000518   Section        0  motor.o(.text.Motor_Brake)
    [Anonymous Symbol]                       0x08000548   Section        0  motor.o(.text.Motor_Init)
    [Anonymous Symbol]                       0x0800054c   Section        0  motor.o(.text.Motor_Run)
    [Anonymous Symbol]                       0x0800059c   Section        0  motor.o(.text.Motor_SpinLeft)
    [Anonymous Symbol]                       0x08000618   Section        0  motor.o(.text.Motor_SpinRight)
    [Anonymous Symbol]                       0x08000694   Section        0  stm32f10x_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x08000698   Section        0  misc.o(.text.NVIC_Init)
    [Anonymous Symbol]                       0x080006f8   Section        0  misc.o(.text.NVIC_PriorityGroupConfig)
    [Anonymous Symbol]                       0x0800070c   Section        0  oled.o(.text.OLED_Init)
    [Anonymous Symbol]                       0x0800081c   Section        0  oled.o(.text.OLED_ShowChar)
    [Anonymous Symbol]                       0x080008ac   Section        0  oled.o(.text.OLED_ShowString)
    [Anonymous Symbol]                       0x080008d8   Section        0  oled.o(.text.OLED_WriteCommand)
    [Anonymous Symbol]                       0x08000a20   Section        0  oled.o(.text.OLED_WriteData)
    [Anonymous Symbol]                       0x08000b70   Section        0  pwm.o(.text.PWM_Init)
    [Anonymous Symbol]                       0x08000c68   Section        0  stm32f10x_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x08000c6c   Section        0  stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd)
    [Anonymous Symbol]                       0x08000c84   Section        0  stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd)
    [Anonymous Symbol]                       0x08000c9c   Section        0  stm32f10x_rcc.o(.text.RCC_GetClocksFreq)
    [Anonymous Symbol]                       0x08000d28   Section        0  stm32f10x_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x08000d2c   Section        0  motor.o(.text.Set_Motor)
    [Anonymous Symbol]                       0x08000da4   Section        0  stm32f10x_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x08000da8   Section        0  system_stm32f10x.o(.text.SystemInit)
    [Anonymous Symbol]                       0x08000eb8   Section        0  stm32f10x_tim.o(.text.TIM_ARRPreloadConfig)
    [Anonymous Symbol]                       0x08000ecc   Section        0  stm32f10x_tim.o(.text.TIM_Cmd)
    [Anonymous Symbol]                       0x08000ee0   Section        0  stm32f10x_tim.o(.text.TIM_OC1Init)
    [Anonymous Symbol]                       0x08000f50   Section        0  stm32f10x_tim.o(.text.TIM_OC1PreloadConfig)
    [Anonymous Symbol]                       0x08000f5c   Section        0  stm32f10x_tim.o(.text.TIM_OC2Init)
    [Anonymous Symbol]                       0x08000fd0   Section        0  stm32f10x_tim.o(.text.TIM_OC2PreloadConfig)
    [Anonymous Symbol]                       0x08000fe0   Section        0  stm32f10x_tim.o(.text.TIM_OC3Init)
    [Anonymous Symbol]                       0x08001054   Section        0  stm32f10x_tim.o(.text.TIM_OC3PreloadConfig)
    [Anonymous Symbol]                       0x08001060   Section        0  stm32f10x_tim.o(.text.TIM_OC4Init)
    [Anonymous Symbol]                       0x080010bc   Section        0  stm32f10x_tim.o(.text.TIM_OC4PreloadConfig)
    [Anonymous Symbol]                       0x080010cc   Section        0  stm32f10x_tim.o(.text.TIM_SetCompare1)
    [Anonymous Symbol]                       0x080010d0   Section        0  stm32f10x_tim.o(.text.TIM_SetCompare2)
    [Anonymous Symbol]                       0x080010d4   Section        0  stm32f10x_tim.o(.text.TIM_SetCompare3)
    [Anonymous Symbol]                       0x080010d8   Section        0  stm32f10x_tim.o(.text.TIM_SetCompare4)
    [Anonymous Symbol]                       0x080010e0   Section        0  stm32f10x_tim.o(.text.TIM_TimeBaseInit)
    [Anonymous Symbol]                       0x0800118c   Section        0  ir_track.o(.text.Track)
    [Anonymous Symbol]                       0x08001278   Section        0  ir_track.o(.text.Track_Init)
    [Anonymous Symbol]                       0x080012a0   Section        0  usart1.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x080012d4   Section        0  usart1.o(.text.USART1_Init)
    [Anonymous Symbol]                       0x08001368   Section        0  stm32f10x_usart.o(.text.USART_ClearFlag)
    [Anonymous Symbol]                       0x08001370   Section        0  stm32f10x_usart.o(.text.USART_Cmd)
    [Anonymous Symbol]                       0x08001384   Section        0  stm32f10x_usart.o(.text.USART_GetFlagStatus)
    [Anonymous Symbol]                       0x08001390   Section        0  stm32f10x_usart.o(.text.USART_ITConfig)
    [Anonymous Symbol]                       0x080013c0   Section        0  stm32f10x_usart.o(.text.USART_Init)
    [Anonymous Symbol]                       0x08001478   Section        0  stm32f10x_usart.o(.text.USART_ReceiveData)
    [Anonymous Symbol]                       0x08001480   Section        0  stm32f10x_it.o(.text.UsageFault_Handler)
    [Anonymous Symbol]                       0x08001484   Section        0  main.o(.text.main)
    i.__scatterload_copy                     0x08001530   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800153e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08001540   Section       14  handlers.o(i.__scatterload_zeroinit)
    ADCPrescTable                            0x0800154e   Data           4  stm32f10x_rcc.o(.rodata.ADCPrescTable)
    [Anonymous Symbol]                       0x0800154e   Section        0  stm32f10x_rcc.o(.rodata.ADCPrescTable)
    APBAHBPrescTable                         0x08001552   Data          16  stm32f10x_rcc.o(.rodata.APBAHBPrescTable)
    [Anonymous Symbol]                       0x08001552   Section        0  stm32f10x_rcc.o(.rodata.APBAHBPrescTable)
    STACK                                    0x20000010   Section     1024  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080000ed   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080000f1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080000f5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080000f5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080000f5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080000f5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080000fd   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080000fd   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000101   Thumb Code     8  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI0_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM2_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM3_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM4_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART2_IRQHandler                        0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART3_IRQHandler                        0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __scatterload                            0x08000125   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x08000125   Thumb Code     0  init.o(.text)
    BusFault_Handler                         0x08000155   Thumb Code     2  stm32f10x_it.o(.text.BusFault_Handler)
    DebugMon_Handler                         0x08000159   Thumb Code     2  stm32f10x_it.o(.text.DebugMon_Handler)
    Delay_ms                                 0x0800015d   Thumb Code    62  delay.o(.text.Delay_ms)
    Delay_us                                 0x0800019d   Thumb Code    36  delay.o(.text.Delay_us)
    EXTI9_5_IRQHandler                       0x080001c1   Thumb Code   182  incontrol.o(.text.EXTI9_5_IRQHandler)
    EXTI_ClearITPendingBit                   0x08000279   Thumb Code    12  stm32f10x_exti.o(.text.EXTI_ClearITPendingBit)
    EXTI_Init                                0x08000285   Thumb Code   104  stm32f10x_exti.o(.text.EXTI_Init)
    GPIO_EXTILineConfig                      0x080002ed   Thumb Code    54  stm32f10x_gpio.o(.text.GPIO_EXTILineConfig)
    GPIO_Init                                0x08000325   Thumb Code   188  stm32f10x_gpio.o(.text.GPIO_Init)
    GPIO_ReadInputDataBit                    0x080003e1   Thumb Code    10  stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit)
    GPIO_WriteBit                            0x080003ed   Thumb Code    12  stm32f10x_gpio.o(.text.GPIO_WriteBit)
    HardFault_Handler                        0x080003f9   Thumb Code     2  stm32f10x_it.o(.text.HardFault_Handler)
    IRremote_Init                            0x080003fd   Thumb Code    96  incontrol.o(.text.IRremote_Init)
    Key_GetNum                               0x0800045d   Thumb Code    60  key.o(.text.Key_GetNum)
    Key_Init                                 0x08000499   Thumb Code    40  key.o(.text.Key_Init)
    MemManage_Handler                        0x080004c1   Thumb Code     2  stm32f10x_it.o(.text.MemManage_Handler)
    Motor_Back                               0x080004c5   Thumb Code    84  motor.o(.text.Motor_Back)
    Motor_Brake                              0x08000519   Thumb Code    46  motor.o(.text.Motor_Brake)
    Motor_Init                               0x08000549   Thumb Code     4  motor.o(.text.Motor_Init)
    Motor_Run                                0x0800054d   Thumb Code    80  motor.o(.text.Motor_Run)
    Motor_SpinLeft                           0x0800059d   Thumb Code   122  motor.o(.text.Motor_SpinLeft)
    Motor_SpinRight                          0x08000619   Thumb Code   122  motor.o(.text.Motor_SpinRight)
    NMI_Handler                              0x08000695   Thumb Code     2  stm32f10x_it.o(.text.NMI_Handler)
    NVIC_Init                                0x08000699   Thumb Code    94  misc.o(.text.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x080006f9   Thumb Code    20  misc.o(.text.NVIC_PriorityGroupConfig)
    OLED_Init                                0x0800070d   Thumb Code   272  oled.o(.text.OLED_Init)
    OLED_ShowChar                            0x0800081d   Thumb Code   142  oled.o(.text.OLED_ShowChar)
    OLED_ShowString                          0x080008ad   Thumb Code    42  oled.o(.text.OLED_ShowString)
    OLED_WriteCommand                        0x080008d9   Thumb Code   326  oled.o(.text.OLED_WriteCommand)
    OLED_WriteData                           0x08000a21   Thumb Code   334  oled.o(.text.OLED_WriteData)
    PWM_Init                                 0x08000b71   Thumb Code   248  pwm.o(.text.PWM_Init)
    PendSV_Handler                           0x08000c69   Thumb Code     2  stm32f10x_it.o(.text.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x08000c6d   Thumb Code    24  stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08000c85   Thumb Code    24  stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08000c9d   Thumb Code   140  stm32f10x_rcc.o(.text.RCC_GetClocksFreq)
    SVC_Handler                              0x08000d29   Thumb Code     2  stm32f10x_it.o(.text.SVC_Handler)
    Set_Motor                                0x08000d2d   Thumb Code   118  motor.o(.text.Set_Motor)
    SysTick_Handler                          0x08000da5   Thumb Code     2  stm32f10x_it.o(.text.SysTick_Handler)
    SystemInit                               0x08000da9   Thumb Code   272  system_stm32f10x.o(.text.SystemInit)
    TIM_ARRPreloadConfig                     0x08000eb9   Thumb Code    20  stm32f10x_tim.o(.text.TIM_ARRPreloadConfig)
    TIM_Cmd                                  0x08000ecd   Thumb Code    18  stm32f10x_tim.o(.text.TIM_Cmd)
    TIM_OC1Init                              0x08000ee1   Thumb Code   112  stm32f10x_tim.o(.text.TIM_OC1Init)
    TIM_OC1PreloadConfig                     0x08000f51   Thumb Code    12  stm32f10x_tim.o(.text.TIM_OC1PreloadConfig)
    TIM_OC2Init                              0x08000f5d   Thumb Code   116  stm32f10x_tim.o(.text.TIM_OC2Init)
    TIM_OC2PreloadConfig                     0x08000fd1   Thumb Code    14  stm32f10x_tim.o(.text.TIM_OC2PreloadConfig)
    TIM_OC3Init                              0x08000fe1   Thumb Code   116  stm32f10x_tim.o(.text.TIM_OC3Init)
    TIM_OC3PreloadConfig                     0x08001055   Thumb Code    12  stm32f10x_tim.o(.text.TIM_OC3PreloadConfig)
    TIM_OC4Init                              0x08001061   Thumb Code    92  stm32f10x_tim.o(.text.TIM_OC4Init)
    TIM_OC4PreloadConfig                     0x080010bd   Thumb Code    14  stm32f10x_tim.o(.text.TIM_OC4PreloadConfig)
    TIM_SetCompare1                          0x080010cd   Thumb Code     4  stm32f10x_tim.o(.text.TIM_SetCompare1)
    TIM_SetCompare2                          0x080010d1   Thumb Code     4  stm32f10x_tim.o(.text.TIM_SetCompare2)
    TIM_SetCompare3                          0x080010d5   Thumb Code     4  stm32f10x_tim.o(.text.TIM_SetCompare3)
    TIM_SetCompare4                          0x080010d9   Thumb Code     6  stm32f10x_tim.o(.text.TIM_SetCompare4)
    TIM_TimeBaseInit                         0x080010e1   Thumb Code   170  stm32f10x_tim.o(.text.TIM_TimeBaseInit)
    Track                                    0x0800118d   Thumb Code   236  ir_track.o(.text.Track)
    Track_Init                               0x08001279   Thumb Code    38  ir_track.o(.text.Track_Init)
    USART1_IRQHandler                        0x080012a1   Thumb Code    50  usart1.o(.text.USART1_IRQHandler)
    USART1_Init                              0x080012d5   Thumb Code   146  usart1.o(.text.USART1_Init)
    USART_ClearFlag                          0x08001369   Thumb Code     6  stm32f10x_usart.o(.text.USART_ClearFlag)
    USART_Cmd                                0x08001371   Thumb Code    20  stm32f10x_usart.o(.text.USART_Cmd)
    USART_GetFlagStatus                      0x08001385   Thumb Code    10  stm32f10x_usart.o(.text.USART_GetFlagStatus)
    USART_ITConfig                           0x08001391   Thumb Code    46  stm32f10x_usart.o(.text.USART_ITConfig)
    USART_Init                               0x080013c1   Thumb Code   182  stm32f10x_usart.o(.text.USART_Init)
    USART_ReceiveData                        0x08001479   Thumb Code     8  stm32f10x_usart.o(.text.USART_ReceiveData)
    UsageFault_Handler                       0x08001481   Thumb Code     2  stm32f10x_it.o(.text.UsageFault_Handler)
    main                                     0x08001485   Thumb Code   168  main.o(.text.main)
    __scatterload_copy                       0x08001531   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800153f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08001541   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    OLED_F8x16                               0x08001562   Data        1520  oled.o(.rodata.OLED_F8x16)
    Region$$Table$$Base                      0x08001b54   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08001b64   Number         0  anon$$obj.o(Region$$Table)
    Car_Mode                                 0x20000000   Data           1  main.o(.bss.Car_Mode)
    IR_Receivecode                           0x20000004   Data           4  incontrol.o(.bss.IR_Receivecode)
    IR_Receiveflag                           0x20000008   Data           1  incontrol.o(.bss.IR_Receiveflag)
    Key                                      0x20000009   Data           1  main.o(.bss.Key)
    RxData                                   0x2000000a   Data           1  usart1.o(.bss.RxData)
    __initial_sp                             0x20000410   Data           0  startup_stm32f10x_md.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00001b64, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00001b64, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000000   Code   RO         1421  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080000ec   0x080000ec   0x00000004   Code   RO         1426    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080000f0   0x080000f0   0x00000004   Code   RO         1429    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         1431    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         1433    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080000f4   0x080000f4   0x00000008   Code   RO         1434    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080000fc   0x080000fc   0x00000000   Code   RO         1436    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080000fc   0x080000fc   0x00000000   Code   RO         1438    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080000fc   0x080000fc   0x00000004   Code   RO         1427    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000100   0x08000100   0x00000024   Code   RO            4    .text               startup_stm32f10x_md.o
    0x08000124   0x08000124   0x00000030   Code   RO         1440    .text               mc_w.l(init.o)
    0x08000154   0x08000154   0x00000002   Code   RO         1403    .text.BusFault_Handler  stm32f10x_it.o
    0x08000156   0x08000156   0x00000002   PAD
    0x08000158   0x08000158   0x00000002   Code   RO         1409    .text.DebugMon_Handler  stm32f10x_it.o
    0x0800015a   0x0800015a   0x00000002   PAD
    0x0800015c   0x0800015c   0x0000003e   Code   RO         1200    .text.Delay_ms      delay.o
    0x0800019a   0x0800019a   0x00000002   PAD
    0x0800019c   0x0800019c   0x00000024   Code   RO         1198    .text.Delay_us      delay.o
    0x080001c0   0x080001c0   0x000000b6   Code   RO         1271    .text.EXTI9_5_IRQHandler  incontrol.o
    0x08000276   0x08000276   0x00000002   PAD
    0x08000278   0x08000278   0x0000000c   Code   RO          409    .text.EXTI_ClearITPendingBit  stm32f10x_exti.o
    0x08000284   0x08000284   0x00000068   Code   RO          397    .text.EXTI_Init     stm32f10x_exti.o
    0x080002ec   0x080002ec   0x00000036   Code   RO          561    .text.GPIO_EXTILineConfig  stm32f10x_gpio.o
    0x08000322   0x08000322   0x00000002   PAD
    0x08000324   0x08000324   0x000000bc   Code   RO          533    .text.GPIO_Init     stm32f10x_gpio.o
    0x080003e0   0x080003e0   0x0000000a   Code   RO          537    .text.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x080003ea   0x080003ea   0x00000002   PAD
    0x080003ec   0x080003ec   0x0000000c   Code   RO          549    .text.GPIO_WriteBit  stm32f10x_gpio.o
    0x080003f8   0x080003f8   0x00000002   Code   RO         1399    .text.HardFault_Handler  stm32f10x_it.o
    0x080003fa   0x080003fa   0x00000002   PAD
    0x080003fc   0x080003fc   0x00000060   Code   RO         1267    .text.IRremote_Init  incontrol.o
    0x0800045c   0x0800045c   0x0000003c   Code   RO         1222    .text.Key_GetNum    key.o
    0x08000498   0x08000498   0x00000028   Code   RO         1220    .text.Key_Init      key.o
    0x080004c0   0x080004c0   0x00000002   Code   RO         1401    .text.MemManage_Handler  stm32f10x_it.o
    0x080004c2   0x080004c2   0x00000002   PAD
    0x080004c4   0x080004c4   0x00000054   Code   RO         1367    .text.Motor_Back    motor.o
    0x08000518   0x08000518   0x0000002e   Code   RO         1365    .text.Motor_Brake   motor.o
    0x08000546   0x08000546   0x00000002   PAD
    0x08000548   0x08000548   0x00000004   Code   RO         1359    .text.Motor_Init    motor.o
    0x0800054c   0x0800054c   0x00000050   Code   RO         1363    .text.Motor_Run     motor.o
    0x0800059c   0x0800059c   0x0000007a   Code   RO         1373    .text.Motor_SpinLeft  motor.o
    0x08000616   0x08000616   0x00000002   PAD
    0x08000618   0x08000618   0x0000007a   Code   RO         1375    .text.Motor_SpinRight  motor.o
    0x08000692   0x08000692   0x00000002   PAD
    0x08000694   0x08000694   0x00000002   Code   RO         1397    .text.NMI_Handler   stm32f10x_it.o
    0x08000696   0x08000696   0x00000002   PAD
    0x08000698   0x08000698   0x0000005e   Code   RO           79    .text.NVIC_Init     misc.o
    0x080006f6   0x080006f6   0x00000002   PAD
    0x080006f8   0x080006f8   0x00000014   Code   RO           77    .text.NVIC_PriorityGroupConfig  misc.o
    0x0800070c   0x0800070c   0x00000110   Code   RO         1348    .text.OLED_Init     oled.o
    0x0800081c   0x0800081c   0x0000008e   Code   RO         1334    .text.OLED_ShowChar  oled.o
    0x080008aa   0x080008aa   0x00000002   PAD
    0x080008ac   0x080008ac   0x0000002a   Code   RO         1336    .text.OLED_ShowString  oled.o
    0x080008d6   0x080008d6   0x00000002   PAD
    0x080008d8   0x080008d8   0x00000146   Code   RO         1326    .text.OLED_WriteCommand  oled.o
    0x08000a1e   0x08000a1e   0x00000002   PAD
    0x08000a20   0x08000a20   0x0000014e   Code   RO         1328    .text.OLED_WriteData  oled.o
    0x08000b6e   0x08000b6e   0x00000002   PAD
    0x08000b70   0x08000b70   0x000000f8   Code   RO         1232    .text.PWM_Init      pwm.o
    0x08000c68   0x08000c68   0x00000002   Code   RO         1411    .text.PendSV_Handler  stm32f10x_it.o
    0x08000c6a   0x08000c6a   0x00000002   PAD
    0x08000c6c   0x08000c6c   0x00000018   Code   RO          740    .text.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08000c84   0x08000c84   0x00000018   Code   RO          738    .text.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08000c9c   0x08000c9c   0x0000008c   Code   RO          734    .text.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08000d28   0x08000d28   0x00000002   Code   RO         1407    .text.SVC_Handler   stm32f10x_it.o
    0x08000d2a   0x08000d2a   0x00000002   PAD
    0x08000d2c   0x08000d2c   0x00000076   Code   RO         1361    .text.Set_Motor     motor.o
    0x08000da2   0x08000da2   0x00000002   PAD
    0x08000da4   0x08000da4   0x00000002   Code   RO         1413    .text.SysTick_Handler  stm32f10x_it.o
    0x08000da6   0x08000da6   0x00000002   PAD
    0x08000da8   0x08000da8   0x00000110   Code   RO           63    .text.SystemInit    system_stm32f10x.o
    0x08000eb8   0x08000eb8   0x00000014   Code   RO         1000    .text.TIM_ARRPreloadConfig  stm32f10x_tim.o
    0x08000ecc   0x08000ecc   0x00000012   Code   RO          960    .text.TIM_Cmd       stm32f10x_tim.o
    0x08000ede   0x08000ede   0x00000002   PAD
    0x08000ee0   0x08000ee0   0x00000070   Code   RO          930    .text.TIM_OC1Init   stm32f10x_tim.o
    0x08000f50   0x08000f50   0x0000000c   Code   RO         1008    .text.TIM_OC1PreloadConfig  stm32f10x_tim.o
    0x08000f5c   0x08000f5c   0x00000074   Code   RO          932    .text.TIM_OC2Init   stm32f10x_tim.o
    0x08000fd0   0x08000fd0   0x0000000e   Code   RO         1010    .text.TIM_OC2PreloadConfig  stm32f10x_tim.o
    0x08000fde   0x08000fde   0x00000002   PAD
    0x08000fe0   0x08000fe0   0x00000074   Code   RO          934    .text.TIM_OC3Init   stm32f10x_tim.o
    0x08001054   0x08001054   0x0000000c   Code   RO         1012    .text.TIM_OC3PreloadConfig  stm32f10x_tim.o
    0x08001060   0x08001060   0x0000005c   Code   RO          936    .text.TIM_OC4Init   stm32f10x_tim.o
    0x080010bc   0x080010bc   0x0000000e   Code   RO         1014    .text.TIM_OC4PreloadConfig  stm32f10x_tim.o
    0x080010ca   0x080010ca   0x00000002   PAD
    0x080010cc   0x080010cc   0x00000004   Code   RO         1070    .text.TIM_SetCompare1  stm32f10x_tim.o
    0x080010d0   0x080010d0   0x00000004   Code   RO         1072    .text.TIM_SetCompare2  stm32f10x_tim.o
    0x080010d4   0x080010d4   0x00000004   Code   RO         1074    .text.TIM_SetCompare3  stm32f10x_tim.o
    0x080010d8   0x080010d8   0x00000006   Code   RO         1076    .text.TIM_SetCompare4  stm32f10x_tim.o
    0x080010de   0x080010de   0x00000002   PAD
    0x080010e0   0x080010e0   0x000000aa   Code   RO          928    .text.TIM_TimeBaseInit  stm32f10x_tim.o
    0x0800118a   0x0800118a   0x00000002   PAD
    0x0800118c   0x0800118c   0x000000ec   Code   RO         1308    .text.Track         ir_track.o
    0x08001278   0x08001278   0x00000026   Code   RO         1296    .text.Track_Init    ir_track.o
    0x0800129e   0x0800129e   0x00000002   PAD
    0x080012a0   0x080012a0   0x00000032   Code   RO         1285    .text.USART1_IRQHandler  usart1.o
    0x080012d2   0x080012d2   0x00000002   PAD
    0x080012d4   0x080012d4   0x00000092   Code   RO         1283    .text.USART1_Init   usart1.o
    0x08001366   0x08001366   0x00000002   PAD
    0x08001368   0x08001368   0x00000006   Code   RO         1160    .text.USART_ClearFlag  stm32f10x_usart.o
    0x0800136e   0x0800136e   0x00000002   PAD
    0x08001370   0x08001370   0x00000014   Code   RO         1118    .text.USART_Cmd     stm32f10x_usart.o
    0x08001384   0x08001384   0x0000000a   Code   RO         1158    .text.USART_GetFlagStatus  stm32f10x_usart.o
    0x0800138e   0x0800138e   0x00000002   PAD
    0x08001390   0x08001390   0x0000002e   Code   RO         1120    .text.USART_ITConfig  stm32f10x_usart.o
    0x080013be   0x080013be   0x00000002   PAD
    0x080013c0   0x080013c0   0x000000b6   Code   RO         1110    .text.USART_Init    stm32f10x_usart.o
    0x08001476   0x08001476   0x00000002   PAD
    0x08001478   0x08001478   0x00000008   Code   RO         1136    .text.USART_ReceiveData  stm32f10x_usart.o
    0x08001480   0x08001480   0x00000002   Code   RO         1405    .text.UsageFault_Handler  stm32f10x_it.o
    0x08001482   0x08001482   0x00000002   PAD
    0x08001484   0x08001484   0x000000ac   Code   RO         1385    .text.main          main.o
    0x08001530   0x08001530   0x0000000e   Code   RO         1444    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800153e   0x0800153e   0x00000002   Code   RO         1445    i.__scatterload_null  mc_w.l(handlers.o)
    0x08001540   0x08001540   0x0000000e   Code   RO         1446    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800154e   0x0800154e   0x00000004   Data   RO          759    .rodata.ADCPrescTable  stm32f10x_rcc.o
    0x08001552   0x08001552   0x00000010   Data   RO          758    .rodata.APBAHBPrescTable  stm32f10x_rcc.o
    0x08001562   0x08001562   0x000005f0   Data   RO         1350    .rodata.OLED_F8x16  oled.o
    0x08001b52   0x08001b52   0x00000002   PAD
    0x08001b54   0x08001b54   0x00000010   Data   RO         1443    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08001b64, Size: 0x00000410, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000        -       0x00000001   Zero   RW         1387    .bss.Car_Mode       main.o
    0x20000001   0x08001b64   0x00000003   PAD
    0x20000004        -       0x00000004   Zero   RW         1273    .bss.IR_Receivecode  incontrol.o
    0x20000008        -       0x00000001   Zero   RW         1274    .bss.IR_Receiveflag  incontrol.o
    0x20000009        -       0x00000001   Zero   RW         1388    .bss.Key            main.o
    0x2000000a        -       0x00000001   Zero   RW         1287    .bss.RxData         usart1.o
    0x2000000b   0x08001b64   0x00000005   PAD
    0x20000010        -       0x00000400   Zero   RW            1    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        98          0          0          0          0       1489   delay.o
       278          0          0          0          5       4251   incontrol.o
       274         32          0          0          0       2902   ir_track.o
       100          0          0          0          0       1691   key.o
       172         10          0          0          2        971   main.o
       114          0          0          0          0       3174   misc.o
       576          0          0          0          0       5219   motor.o
      1116          0       1520          0          0       8135   oled.o
       248          0          0          0          0       2946   pwm.o
        36          8        236          0       1024        824   startup_stm32f10x_md.o
       116          0          0          0          0       2754   stm32f10x_exti.o
       264          0          0          0          0       5908   stm32f10x_gpio.o
        18          0          0          0          0       1024   stm32f10x_it.o
       188          0         20          0          0       8235   stm32f10x_rcc.o
       714          0          0          0          0      29621   stm32f10x_tim.o
       272          0          0          0          0       8567   stm32f10x_usart.o
       272          0          0          0          0       2565   system_stm32f10x.o
       196          0          0          0          1       3727   usart1.o

    ----------------------------------------------------------------------
      5120         <USER>       <GROUP>          0       1040      94003   Object Totals
         0          0         16          0          0          0   (incl. Generated)
        68          0          2          0          8          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o

    ----------------------------------------------------------------------
        98         <USER>          <GROUP>          0          0         68   Library Totals
         0          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

        98         18          0          0          0         68   mc_w.l

    ----------------------------------------------------------------------
        98         <USER>          <GROUP>          0          0         68   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      5218         68       1794          0       1040      93903   Grand Totals
      5218         68       1794          0       1040      93903   ELF Image Totals
      5218         68       1794          0          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 7012 (   6.85kB)
    Total RW  Size (RW Data + ZI Data)              1040 (   1.02kB)
    Total ROM Size (Code + RO Data + RW Data)       7012 (   6.85kB)

==============================================================================

